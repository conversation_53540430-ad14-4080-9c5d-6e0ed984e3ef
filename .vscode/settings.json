{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "eslint.workingDirectories": [{"mode": "auto"}], "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["classNames?\\s*:\\s*{[^}]*}", "(?:'|\"|`)([^'\"]*?)(?:'|\"|`)"], ["className\\s*=\\s*['\"`]([^'\"`]*)['\"`]"]], "eslint.format.enable": true, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "cSpell.words": ["aipt", "<PERSON><PERSON><PERSON><PERSON>", "Apifox", "<PERSON><PERSON><PERSON>", "axios", "banshu", "boardscript", "cascader", "chtml", "creater", "doubleclick", "esbuild", "frameupdate", "jsbridge", "katex", "Ketex", "longpress", "MATHJAX", "mathlive", "<PERSON><PERSON><PERSON>", "mhchem", "msvideo", "MVVM", "Offthread", "oklab", "oklch", "Popconfirm", "preact", "quicktime", "rgba", "serviceworker", "shadcn", "sonner", "streamsaver", "UCENTER", "umij<PERSON>", "veplayer", "viewmodel", "viewmodels", "Volcengine", "xgplayer", "xia<PERSON><PERSON><PERSON>", "xmind", "xyflow", "weboffice", "<PERSON><PERSON><PERSON>", "ahooks"], "css.format.spaceAroundSelectorSeparator": true, "css.lint.float": "error", "css.lint.unknownAtRules": "warning", "cssrem.vw": true, "css.customData": ["./.vscode/css.json"], "svg.preview.background": "transparent", "typescript.tsdk": "node_modules/typescript/lib", "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.preferences.includeCompletionsForModuleExports": true, "typescript.preferences.includeCompletionsWithInsertText": true, "typescript.workspaceSymbols.scope": "currentProject", "typescript.references.enabled": true, "typescript.implementationsCodeLens.enabled": true, "typescript.referencesCodeLens.enabled": true, "typescript.referencesCodeLens.showOnAllFunctions": true, "editor.gotoLocation.multipleReferences": "peek", "editor.gotoLocation.multipleDefinitions": "peek", "editor.gotoLocation.multipleDeclarations": "peek", "editor.gotoLocation.multipleImplementations": "peek", "[svg]": {"editor.defaultFormatter": "jock.svg"}, "augment.disableFocusOnAugmentPanel": false, "augment.enableEmptyFileHint": false, "augment.conflictingCodingAssistantCheck": false, "augment.advanced": null, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}