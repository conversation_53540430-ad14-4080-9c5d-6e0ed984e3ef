import "@/app/app.css";
import type { Metada<PERSON>, Viewport } from "next";
import { Providers } from "./providers";

// import { Resource_Han_Rounded_SC } from "./style/fonts";

export const metadata: Metadata = {
  title: "课中",
  description: "课中",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/images/favicon.ico" sizes="32x32" />
      </head>
      <body>
        <Providers>
          <div className="text-foreground bg-background">{children}</div>
        </Providers>
      </body>
    </html>
  );
}
