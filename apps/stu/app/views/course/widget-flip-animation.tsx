import { FlipDirection, WidgetType } from "@/types/app/course";
import {
  ReadonlySignal,
  useComputed,
  useSignal,
} from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import { motion } from "framer-motion";
import { FC } from "react";

export const WidgetFlipAnimation: FC<{
  show: ReadonlySignal<boolean>;
  direction: ReadonlySignal<FlipDirection>;
  onAnimationComplete?: () => void;
  flipTargetWidgetType: ReadonlySignal<WidgetType | undefined>;
}> = ({ show, direction, onAnimationComplete, flipTargetWidgetType }) => {
  const isReady = useSignal(false);
  const variants = {
    show: (dir: FlipDirection) => {
      return {
        y: dir === "next" ? "calc(var(--spacing)*-32)" : "0",
        transition: {
          duration: 0.8,
        },
      };
    },
    ready: (dir: FlipDirection) => {
      return {
        y: dir === "next" ? "100%" : "calc(-100% - var(--spacing)*32)",
        transition: {
          duration: 0.01,
        },
      };
    },
    hide: (dir: FlipDirection) => ({
      y: dir === "next" ? "100%" : "calc(-100% - var(--spacing)*32)",
      transition: {
        duration: 0,
      },
    }),
  };

  const animate = useComputed(() => {
    if (show.value) {
      return isReady.value ? "show" : "ready";
    }
    return "hide";
  });
  return (
    <motion.div
      custom={direction.value}
      initial="hide"
      variants={variants}
      animate={animate.value}
      className="z-100 pointer-events-none absolute left-0 top-0 h-full w-full"
      onAnimationComplete={(latest) => {
        if (latest === "hide") {
          isReady.value = false;
        }
        if (latest === "ready") {
          isReady.value = true;
        }
        if (latest === "show") {
          onAnimationComplete?.();
        }
      }}
    >
      {direction.value === "next" && (
        <div
          data-name="next"
          className={cn(
            "bg-linear-to-t h-32 w-full via-20%",
            flipTargetWidgetType?.value === "exercise"
              ? "from-bg-exercise via-bg-exercise to-bg-exercise/0"
              : "from-bg-guide via-bg-guide to-bg-guide/0"
          )}
        />
      )}
      <div
        className={cn(
          "h-full w-full",
          flipTargetWidgetType?.value === "exercise"
            ? "bg-bg-exercise"
            : "bg-bg-guide"
        )}
      />
      {direction.value === "prev" && (
        <div
          data-name="prev"
          className={cn(
            "bg-linear-to-b h-32 w-full via-20%",
            flipTargetWidgetType?.value === "exercise"
              ? "from-bg-exercise via-bg-exercise to-bg-exercise/0"
              : "from-bg-guide via-bg-guide to-bg-guide/0"
          )}
        />
      )}
    </motion.div>
  );
};
