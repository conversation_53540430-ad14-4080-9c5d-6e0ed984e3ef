import { useClientContext } from "@/app/providers/client-provider";
import { cn } from "@repo/ui/lib/utils";
import dynamic from "next/dynamic";
import { FC, useCallback } from "react";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";
import { useVideoViewContext } from "./video-view-context";

const VolcengineVideo = dynamic(
  () => import("@repo/core/components/volcengine-video/volcengine-video"),
  {
    ssr: false,
  }
);
export const VideoPlayerView: FC<{ className?: string }> = ({ className }) => {
  const { studentUserInfo } = useClientContext();
  const {
    data,
    refVolcenginePlayer,
    playRate,
    set3XPlayRate,
    resetPlayRate,
    togglePlayerControls,
    togglePlay,
    active,
  } = useVideoViewContext();
  const { url, duration } = data;
  const longPressHandlers = useLongPress(set3XPlayRate, {
    onFinish: () => {
      resetPlayRate();
      // trackEventWithLessonId("doc_fast_forward_longpress");
    },
    // onCancel: () => {
    //   console.log("onCancel");
    //   resetPlayRate();
    // },
    detect: LongPressEventType.Touch,
  });
  const handleClick = useCallback(() => {
    togglePlayerControls();
  }, [togglePlayerControls]);

  const handleDoubleClick = useCallback(() => {
    togglePlay();
    // trackEventWithLessonId("doc_play_pause_doubleclick");
  }, [togglePlay]);

  const doubleTapHandlers = useDoubleTap(handleDoubleClick, 300, {
    onSingleTap: handleClick,
  });

  return (
    <div
      data-name="video-player"
      className={cn("h-screen w-full overflow-y-hidden", className)}
      {...doubleTapHandlers}
      {...longPressHandlers()}
    >
      {active && (
        <VolcengineVideo
          className="flex w-full flex-col items-center justify-center"
          ref={refVolcenginePlayer}
          src={url}
          playRate={playRate.value}
          userId={studentUserInfo?.userId}
          tag="视频组件"
          // 如果视频时长大于10分钟，则使用宽度自适应，否则使用高度自适应, 用于临时解决判断是否日语视频的问题
          fitWidth={duration >= 10 * 60}
        />
      )}
    </div>
  );
};
