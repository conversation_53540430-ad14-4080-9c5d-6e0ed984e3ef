// apps/stu/app/ui-kit/progress-bar-demo2/page.tsx
"use client";

import {
  ProgressBar,
  useProgressBar,
} from "@repo/core/exercise/components/ProgressBar";
import { useCallback, useState } from "react";

export default function ProgressBarDemoPage() {
  const [backendProgress, setBackendProgress] = useState(0);
  const STEP = 10;
  const [isFirstAttempt, setIsFirstAttempt] = useState(true);

  const { progressBarProps, handleProgress, reset, isAnimating } =
    useProgressBar({
      initialProgress: backendProgress,
    });
  const [correctComboCount, setCorrectComboCount] = useState(0);

  // 回答正确的处理
  const handleCorrectAnswer = () => {
    // It's important to calculate the *next* state, not use the current one.
    const newCorrectComboCount = correctComboCount + 1;
    const newProgress = Math.min(100, backendProgress + STEP);

    // ✨ Enqueue just one animation with the *final* state
    handleProgress({
      type: "correct",
      text:
        newCorrectComboCount > 1 ? `连对x${newCorrectComboCount}` : "太棒了!",
      progress: newProgress,
      correctComboCount: newCorrectComboCount,
      isAllCorrect: newProgress >= 100,
    });

    // Now, update the backend state
    setBackendProgress(newProgress);
    setCorrectComboCount(newCorrectComboCount);
    setIsFirstAttempt(true);
  };

  // 客观题第一次回答错误
  const handleFirstIncorrectAnswer = useCallback(() => {
    // 1. 显示错误反馈，但进度条不前进
    handleProgress({
      type: "incorrect",
      text: "再想想，你还有一次机会!",
      // ✨ Pass the current progress from the hook's props
      progress: progressBarProps.progress,
      correctComboCount: 0,
      isAllCorrect: false,
    });

    // 2. 更新状态
    setIsFirstAttempt(false);
    setCorrectComboCount(0);
  }, [handleProgress, progressBarProps.progress]);

  // 客观题第二次回答错误
  const handleSecondIncorrectAnswer = useCallback(() => {
    const newProgress = Math.min(100, backendProgress + STEP);

    // 1. 显示错误反馈，并指定前进后的进度
    handleProgress({
      type: "incorrect",
      text: "答案不对，但我们继续下一题!",
      progress: newProgress,
      correctComboCount: 0,
      isAllCorrect: false,
    });

    // 2. 更新“后端”状态
    setBackendProgress(newProgress);
    setIsFirstAttempt(true);
    setCorrectComboCount(0);
  }, [backendProgress, handleProgress]);

  // ✨ New handler for testing the animation queue
  const handleTestQueue = () => {
    // A quick burst of 3 "correct" answers to test the queue
    for (let i = 1; i <= 3; i++) {
      const newProgress = Math.min(100, backendProgress + i * STEP);
      const newCombo = correctComboCount + i;
      handleProgress({
        type: "correct",
        text: `队列 +${i}`,
        progress: newProgress,
        correctComboCount: newCombo,
        isAllCorrect: newProgress >= 100,
      });
    }

    // Update the "backend" state to the final value after queueing all animations
    setBackendProgress((prev) => Math.min(100, prev + 3 * STEP));
    setCorrectComboCount((prev) => prev + 3);
  };

  const handleReset = () => {
    reset();
    setBackendProgress(0);
    setCorrectComboCount(0);
    setIsFirstAttempt(true);
  };

  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center gap-16 bg-[#F3FAFF] font-sans">
      <h1 className="text-3xl font-bold text-gray-800">
        进度条演示 (后端驱动进度)
      </h1>

      <ProgressBar {...progressBarProps} />

      {/* --- Control Buttons --- */}
      <div className="grid grid-cols-2 gap-4">
        <button
          onClick={handleCorrectAnswer}
          disabled={isAnimating}
          className="progress-demo-button bg-green-500 hover:bg-green-600"
        >
          回答正确
        </button>
        <button
          onClick={handleFirstIncorrectAnswer}
          disabled={isAnimating || !isFirstAttempt}
          className="progress-demo-button bg-yellow-500 hover:bg-yellow-600"
        >
          第一次答错
        </button>
        <button
          onClick={handleSecondIncorrectAnswer}
          disabled={isAnimating || isFirstAttempt}
          className="progress-demo-button bg-orange-500 hover:bg-orange-600"
        >
          第二次答错
        </button>
        {/* ✨ New "Test Queue" Button */}
        <button
          onClick={handleTestQueue}
          disabled={isAnimating}
          className="progress-demo-button bg-blue-500 hover:bg-blue-600"
        >
          测试队列 (连点3次)
        </button>
        <button
          onClick={handleReset}
          disabled={isAnimating}
          className="progress-demo-button col-span-2 bg-red-500 hover:bg-red-600"
        >
          重置
        </button>
      </div>

      {/* --- 当前状态提示 --- */}
      <div className="text-center">
        <p className="text-lg font-semibold text-gray-700">
          当前状态: {isFirstAttempt ? "第一次尝试" : "第二次尝试"}
        </p>
      </div>

      {/* --- State Display for Debugging --- */}
      <div className="mt-4 w-80 rounded-lg bg-white p-4 shadow">
        <h3 className="mb-2 font-bold">当前状态说明:</h3>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex justify-between">
            <span>后端进度:</span>
            <span className="font-medium">{backendProgress}%</span>
          </div>
          <div className="flex justify-between">
            <span>动画状态:</span>
            <span className="font-medium">
              {isAnimating ? "播放中" : "静止"}
            </span>
          </div>
          <div className="flex justify-between">
            <span>尝试次数:</span>
            <span className="font-medium">
              {isFirstAttempt ? "第一次" : "第二次"}
            </span>
          </div>
          <div className="flex justify-between">
            <span>连击次数:</span>
            <span className="font-medium">{correctComboCount}次</span>
          </div>
          <div className="flex justify-between">
            <span>当前进度:</span>
            <span className="font-medium">{progressBarProps.progress}%</span>
          </div>
        </div>
      </div>

      {/* --- 内联样式用于按钮 --- */}
      <style>{`
        .progress-demo-button {
          padding: 10px 20px;
          border: none;
          border-radius: 8px;
          color: white;
          font-weight: bold;
          cursor: pointer;
          transition: background-color 0.2s, opacity 0.2s;
          text-align: center;
          line-height: 1.2;
        }
        .progress-demo-button:disabled {
          background-color: #a0a0a0 !important;
          cursor: not-allowed;
          opacity: 0.6;
        }
      `}</style>
    </div>
  );
}
