"use client";

import { getStatusBarHeight } from "@/app/utils/device";
import React, { useRef } from "react";

interface PrivacyPolicyBriefProps {
  onBack: () => void;
}

// 小鹿爱学隐私政策简明版内容
const PrivacyPolicyBriefContent = () => (
  <div className="space-y-4 text-sm">
    <div className="space-y-4 leading-relaxed text-gray-700">
      <p>
        小鹿爱学深知个人信息对您的重要性，我们将按照法律法规的规定，保护您的个人信息及隐私安全。本简明指引将帮助您直观、简明地了解我们是如何收集、处理使用个人信息以及如何保护您的隐私。更多详细信息请阅读完整的小鹿爱学隐私政策。
      </p>

      <div>
        <h2 className="mb-2 text-base font-semibold text-gray-900">
          一、我们如何收集、使用信息
        </h2>
      </div>

      <div>
        <h3 className="mb-1 text-sm font-medium text-gray-900">1.1 账号信息</h3>
        <p className="mb-2">
          小鹿爱学账号是基于我们与您或您的被监护人所在组织的合作，根据您或您的被监护人所在组织提供的您或您的被监护人的真实姓名、班级、年级、学号、学校等信息后为您或您的被监护人生成的服务账号，您无需自行注册，可直接使用您所在组织的管理员分配的账号登录并使用本服务。
        </p>
        <p>您可以通过您的被监护人所在的组织与我们联系，对您的账号进行找回。</p>
      </div>

      <div>
        <h3 className="mb-1 text-sm font-medium text-gray-900">1.2 使用信息</h3>
        <p className="mb-2">
          <span className="font-medium">【学习与做题】</span>
          为提供本服务、针对性学习资料、学情分析服务，我们会收集您或您被监护人的上课情况、学习时长及做题情况，分析后向其所在组织展示学情信息，该组织将据此发布针对性学习任务。您或您被监护人作答时拍摄
          /
          发布图片，我们会依功能类型请求相机、相册权限；拒绝授权将无法使用该功能，但不影响其他功能。
        </p>
        <p className="mb-2">
          <span className="font-medium">【问一问及评论提问】</span>
          课程学习功能下设有 "问一问及评论提问"
          功能，您或您监护人可随时提问并与服务方、老师、同学互动。我们会收集提问内容，以协助解答问题、提供解题思路。提问及发布内容的可见范围，将按您或您监护人在产品端的选择（如公开、仅自己可见等）处理，具体以当时产品选项及实际选择为准。
        </p>
        <p>
          <span className="font-medium">【笔记本及错题本】</span>
          您或您被监护人可发布笔记本中的笔记、错题本中的错题记录，我们会收集这些内容。使用笔记本或错题本功能时，上传信息仅您或您被监护人及授课老师可见。此外，拍摄错题或发布图片时，我们会依功能类型请求相机、相册权限；拒绝授权将无法使用该功能，但不影响其他功能。
        </p>
      </div>

      <div>
        <h3 className="mb-1 text-sm font-medium text-gray-900">
          1.3 麦克风/摄像头/相册
        </h3>
        <p>
          摄像头（相机）、麦克风、相册等敏感权限均不会默认开启，仅在您明确授权后，才会在实现特定功能或服务时开启使用，您也可以随时撤回授权。且即便在您授权后，我们也仅在相关功能或服务运行期间收集必要信息，在功能或服务无需使用时，我们不会收集该等信息。
        </p>
      </div>

      <div>
        <h2 className="mb-2 text-base font-semibold text-gray-900">
          二、如何更好地控制个人信息的使用
        </h2>
      </div>

      <div>
        <h3 className="mb-1 text-sm font-medium text-gray-900">
          2.1 修改个人信息
        </h3>
        <p className="mb-2">
          您可以在"小鹿爱学"的"我的"页面点击昵称、头像进行查阅、复制，可以点击昵称、头像、地区、年级进行更正。
        </p>
        <p>
          如果您需要更正、删除昵称、头像、年级、地区，需向您或您的被监护人所在组织提出申请，由您或您的被监护人所在组织与我们联系。
        </p>
      </div>

      <div>
        <h3 className="mb-1 text-sm font-medium text-gray-900">2.2 注销帐号</h3>
        <p>
          根据我们与您或您的被监护人所在组织的合作协议及合作模式的特殊性，注销帐号需经过您或您的被监护人所在组织的同意，您或您的被监护人退出所在组织或所在组织解散的，我们将会删除或匿名化处理您或您的被监护人在该组织的相关个人信息，但法律法规另有规定或根据您或您的被监护人所在组织需要就相关个人信息另行处理的除外。
        </p>
      </div>
    </div>
  </div>
);

export const PrivacyPolicyBriefView: React.FC<PrivacyPolicyBriefProps> = ({
  onBack: _onBack,
}) => {
  const statusBarHeight = useRef(getStatusBarHeight());

  return (
    <div
      className="relative flex h-screen flex-col bg-[#F7F6F5]"
      style={{
        paddingTop: statusBarHeight.current + "px",
      }}
    >
      <div className="font-resource-han-rounded flex-1 overflow-y-auto px-8 py-8">
        <PrivacyPolicyBriefContent />
      </div>
    </div>
  );
};
