"use client";

import { getStatusBarHeight } from "@/app/utils/device";
import React, { useRef } from "react";

interface ChildrenPrivacyProtectionProps {
  onBack: () => void;
}

// 儿童个人信息保护指引内容
const ChildrenPrivacyProtectionContent = () => (
  <div className="space-y-6 text-sm">
    <div className="space-y-4">
      <div className="text-gray-700 leading-relaxed">
        <p className="mb-4">
          欢迎您选择由北京银河智学教育科技有限公司及其关联方（以下简称"我们"）开发、运营、提供的相关产品和服务（以下简称"本服务"）！我们将按照法律法规的规定，保护未满十四周岁的未成年人（以下简称"儿童"）的个人信息及隐私安全。本《儿童个人信息保护指引》旨在向法定监护人或儿童的父母（以下简称"监护人"）和儿童说明我们如何收集和处理儿童个人信息。
        </p>
      </div>

      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-3">1. 儿童利益优先原则</h2>
        <div className="text-gray-700 leading-relaxed space-y-3">
          <p>
            为保障儿童合法权益，本服务在提供课程服务、教材资料时，严格按 "最小必要" 原则收集处理儿童信息，所有信息操作均以儿童利益为首要考量，具体如下：
          </p>
          
          <div className="ml-4">
            <h3 className="font-medium text-gray-900 mb-2">1.1 尊重与保障儿童隐私权</h3>
            <p className="mb-3">
              在 "儿童利益优先" 指引下，本服务收集使用儿童个人信息均围绕 "提供适配课程、教材资料" 展开，且提前告知监护人及儿童数据类型、用途和范围。同时通过加密存储、权限管控等手段，杜绝数据滥用泄露，全流程保障儿童隐私。
            </p>
            
            <h3 className="font-medium text-gray-900 mb-2">1.2 尊重与保障儿童发展权</h3>
            <p className="mb-3">
              基于 "儿童利益优先"，本服务收集使用儿童学习必要数据（如学习进度和结果），匹配适合的课程、教材和题库，助力儿童学习。本服务设计融入友好交互与鼓励机制，营造温暖学习氛围，让儿童感受关怀，保障发展权。
            </p>
            
            <h3 className="font-medium text-gray-900 mb-2">1.3 保障儿童的基本安全</h3>
            <p>
              本服务严格遵循《未成年人保护法》《个人信息保护法》等法律法规，全流程保障儿童安全。采用加密等技术护信息安全，杜绝歧视与侵害行为，打造安全健康的学习环境。
            </p>
          </div>
        </div>
      </section>

      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-3">2. 儿童信息的收集、使用</h2>
        <div className="text-gray-700 leading-relaxed space-y-3">
          <div className="ml-4">
            <h3 className="font-medium text-gray-900 mb-2">2.1 严格限制收集儿童个人信息的类型</h3>
            <div className="space-y-2">
              <p>
                a. 仅在获得您（监护人）授权同意、提供课程 / 教材等服务必需，或符合法律规定要求的合法前提下，我们才会向您及被监护人收集合理且必要的个人信息，杜绝无关信息收集。
              </p>
              <p>b. 为提供本服务，我们收集最小必要的儿童个人信息包括：</p>
              <ul className="list-disc ml-6 space-y-1">
                <li>真实姓名、班级、年级、学号、学校，用于创建账号、账号信息展示、提供对应的课程和学习资料；</li>
                <li>上课情况、学习时长、答题情况、错题记录、问一问和评论提问功能下提出的问题，用以实时跟进学习情况，协助儿童学习，帮助儿童学习过程中遇到的实际问题解答。</li>
                <li>部分设备信息和日志信息，用以排查问题、运营和安全保障。</li>
              </ul>
            </div>
            
            <h3 className="font-medium text-gray-900 mb-2 mt-4">2.2 严格限定未成年人个人信息的使用目的</h3>
            <div className="space-y-2">
              <p>
                a. 为实现 "因材施教"，我们会依据被监护人的学习数据（如已学课程、答题记录等），提供适配的学习内容，准确高效地解答儿童的学习问题。
              </p>
              <p>
                b. 为避免商业信息干扰儿童学习，我们不在儿童账号内添加任何网络游戏链接，也绝不推送与教学无关的广告内容，为儿童营造纯粹的学习环境。
              </p>
              <p>
                c. 本服务收集的儿童信息，仅用于 "提供课程服务、教材资料"等核心教育学习目的，绝不用于任何商业目的，包括但不限于商业广告投放、营销活动等。
              </p>
              <p>
                d. 我们会与儿童所在的组织根据法律规定签署相关协议并约定各自的权利和义务，我们会按照法律法规的要求、技术规范以及任何适当的保密和安全措施进行上述合作。
              </p>
            </div>
            
            <h3 className="font-medium text-gray-900 mb-2 mt-4">2.3 保障儿童安全的信息保护措施</h3>
            <div className="space-y-2">
              <p>
                a. 我们采用高强度加密技术、信息匿名化处理等手段，对儿童信息进行安全防护，同时通过安全机制抵御恶意攻击，防止信息被非法获取。
              </p>
              <p>
                b. 建立严格的信息访问与使用权限体系，仅允许授权人员接触信息，并定期开展数据安全与技术安全审计，全方位保障儿童信息安全。
              </p>
            </div>
            
            <h3 className="font-medium text-gray-900 mb-2 mt-4">2.4 儿童和青少年个人信息存储和删除措施</h3>
            <p>
              我们在境内运营过程中收集和产生的儿童个人信息存储于中华人民共和国境内，不会传输或存储至境外。
            </p>
          </div>
        </div>
      </section>

      <section>
        <h2 className="text-lg font-semibold text-gray-900 mb-3">3. 联系我们</h2>
        <div className="text-gray-700 leading-relaxed">
          <p>
            如果您对您的被监护人的帐号、使用产品和服务的情况或其他方面有任何疑问，您可以与您的被监护人所在组织联系，也可以通过**********************联系我们。我们将尽快审核所涉问题，并在验证您的监护人身份后，根据你的请求和法律法规的规定进行处理。
          </p>
        </div>
      </section>
    </div>
  </div>
);

export const ChildrenPrivacyProtectionView: React.FC<ChildrenPrivacyProtectionProps> = ({
  onBack: _onBack,
}) => {
  const statusBarHeight = useRef(getStatusBarHeight());

  return (
    <div
      className="relative flex h-screen flex-col bg-[#F7F6F5]"
      style={{
        paddingTop: statusBarHeight.current + "px",
      }}
    >
      <div className="font-resource-han-rounded flex-1 overflow-y-auto px-8 py-8">
        <ChildrenPrivacyProtectionContent />
      </div>
    </div>
  );
};
