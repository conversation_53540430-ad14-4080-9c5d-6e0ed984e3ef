"use client";

import { getStatusBarHeight } from "@/app/utils/device";
import React, { useRef } from "react";

interface AlgorithmModelDisclosureProps {
  onBack: () => void;
}

// 算法及模型公示信息内容
const AlgorithmModelDisclosureContent = () => (
  <div className="space-y-6 text-sm">
    <div className="space-y-4">
      <section>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300 bg-white text-sm">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900 whitespace-nowrap">
                  备案算法/模型名称
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  备案类型
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  备案号
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  公示链接
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="whitespace-nowrap border border-gray-300 px-4 py-3 text-gray-700">
                  豆包大模型算法
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  深度合成服务算法
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  网信算备110108823483901230031号
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  <a
                    href="https://www.cac.gov.cn/2023-09/01/c_1695224377544009.htm"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="break-all text-blue-600 underline hover:text-blue-800"
                  >
                    https://www.cac.gov.cn/2023-09/01/c_1695224377544009.htm
                  </a>
                </td>
              </tr>
              <tr>
                <td className="whitespace-nowrap border border-gray-300 px-4 py-3 text-gray-700">
                  豆包大模型
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  生成式人工智能
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  Beijing-YunQue-20230821
                </td>
                <td className="border border-gray-300 px-4 py-3 text-gray-700">
                  <a
                    href="https://www.cac.gov.cn/2024-04/02/c_1713729983803145.htm"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="break-all text-blue-600 underline hover:text-blue-800"
                  >
                    https://www.cac.gov.cn/2024-04/02/c_1713729983803145.htm
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>
    </div>
  </div>
);

export const AlgorithmModelDisclosureView: React.FC<
  AlgorithmModelDisclosureProps
> = ({ onBack: _onBack }) => {
  const statusBarHeight = useRef(getStatusBarHeight());

  return (
    <div
      className="relative flex h-screen flex-col bg-[#F7F6F5]"
      style={{
        paddingTop: statusBarHeight.current + "px",
      }}
    >
      <div className="font-resource-han-rounded flex-1 overflow-y-auto px-8 py-8">
        <AlgorithmModelDisclosureContent />
      </div>
    </div>
  );
};
