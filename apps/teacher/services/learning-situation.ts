import { r } from "../libs/axios";

export interface AskDataParams {
  studentName: string;
  courseName: string;
  startTime: number;
  endTime: number;
  classId: number;
  subjectId: number;
  page: number;
  pageSize: number;
  sortBy?: string;
  sortType?: string;
}
export interface AskDataResponse {
  pageInfo: {
    page: number;
    pageSize: number;
    total: number;
  };
  list: Array<{
    studentId: number;
    subjectId: number;
    userQuestion: string;
    AIAnswer: string;
    sendTime: number;
    knowledgeId: number;
    className: string;
    studentName: string;
    subjectName: string;
    knowledgeName: string;
    gradeName: string;
  }>;
}
export const studentAskDataList = async (params: AskDataParams) => {
  return r.post<AskDataResponse>(
    "/learning-situation/student-ask-list",
    params
  );
};
