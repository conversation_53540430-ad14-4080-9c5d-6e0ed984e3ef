import { useMount, useUnmount, useUpdateEffect } from "ahooks";
import { RefObject, useCallback, useRef, useState } from "react";
// 检测文本是否溢出的自定义 Hook
export function useIsTextOverflow<T extends HTMLElement>(
  deps: any[] = []
): [RefObject<T | null>, boolean] {
  const ref = useRef<T>(null);
  const [isOverflow, setIsOverflow] = useState(false);

  const checkOverflow = useCallback(() => {
    if (ref.current) {
      // 检查内容高度是否大于容器高度（考虑1px容差）
      const isOverflowing =
        ref.current.scrollHeight > ref.current.clientHeight + 1;
      setIsOverflow(isOverflowing);
    }
  }, []);
  useMount(() => {
    checkOverflow();
    window.addEventListener("resize", checkOverflow);
  });
  useUnmount(() => {
    window.removeEventListener("resize", checkOverflow);
  });
  useUpdateEffect(() => {
    checkOverflow();
    // 添加窗口大小变化监听
    window.addEventListener("resize", checkOverflow);
    return () => window.removeEventListener("resize", checkOverflow);
  }, deps);

  return [ref, isOverflow];
}
