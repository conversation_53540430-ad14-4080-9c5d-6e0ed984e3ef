"use client";
import { LazyDataTable } from "@/app/homework/[id]/_components/tabs/Report";
import { TableColumn } from "@/app/homework/_components/DataTable";
// import { useOptions } from "@/app/homework/hooks/useOptionsV2";
import { PageHeader } from "@/components/PageHeader";
import { getSubjectClassList } from "@/services";
import {
  AskDataParams,
  AskDataResponse,
  studentAskDataList,
} from "@/services/learning-situation";
import { Button } from "@/ui/button";
import { DatePicker, DateRangeValue } from "@/ui/datePicker";
import { Input } from "@/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/select";
import { toast } from "@/ui/toast";
import { cn } from "@/utils";
import { SortingState } from "@tanstack/react-table";
import { useMount, useRequest } from "ahooks";
import dayjs from "dayjs";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useRouter } from "next/navigation";
import { ChangeEvent, useCallback, useMemo, useState } from "react";
import { useIsTextOverflow } from "./hooks";

const TriggerInput = ({
  children,
  isOpen,
  className,
  onClick,
}: React.ComponentProps<"div"> & { isOpen: boolean }) => {
  return (
    <div
      className={cn(
        "border-line-1 shadow-xs flex h-9 items-center justify-between gap-2 whitespace-nowrap rounded-[0.375rem] border bg-white px-4 font-[500] shadow-none transition-[color,box-shadow] transition-all duration-300",
        isOpen
          ? "border-primary-1 text-primary-1 border bg-white"
          : "text-gray-2 hover:bg-gray-100",
        className
      )}
      onClick={(e) => onClick?.(e)}
    >
      <span className="flex flex-1 justify-start overflow-hidden text-ellipsis whitespace-nowrap text-sm">
        {children}
      </span>
      <ChevronDown
        className={cn(
          "h-4 min-w-4 transition-transform duration-300",
          isOpen
            ? "text-primary-1 rotate-[-180deg]"
            : "text-gray-2 rotate-[0deg]"
        )}
      />
    </div>
  );
};
export default function Ask() {
  const router = useRouter();
  const [subjectOpen, setSubjectOpen] = useState(false);
  const [classOpen, setClassOpen] = useState(false);
  // const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState<{
    subject: number;
    startDate: Date;
    endDate: Date;
    page: number;
    classId: number;
    pageSize: number;
    // sortBy?: "sendTime";
    // sortType?: "asc" | "desc";
    studentName: string;
    courseName: string;
    total: number;
  }>({
    subject: 0,
    classId: 0,
    studentName: "",
    courseName: "",
    startDate: dayjs().subtract(6, "day").startOf("date").toDate(),
    endDate: dayjs().endOf("date").toDate(),
    page: 0,
    pageSize: 20,
    total: 0,
  });
  const [pageChange, setPageChange] = useState(false);
  const [sort, setSort] = useState<{
    sortBy?: "sendTime";
    sortType?: "asc" | "desc";
  }>({});
  const [answerOpen, setAnswerOpen] = useState<{
    [key: string]: { question: boolean; answer: boolean };
  }>({});
  const [askData, setAskData] = useState<
    (AskDataResponse["list"][0] & { id: number })[]
  >([]);
  const [subjectClass, setSubjectClass] = useState<
    Array<{
      subjectKey: number;
      subjectName: string;
      gradeClasses: Array<{
        gradeID: number;
        gradeName: string;
        classID: number;
        className: string;
      }>;
    }>
  >([]);
  //   const {
  //     subjectClassFlatList,
  //     subjectClassTreeList,
  //     useFetchOptionData,
  //     classFlatList,
  //   } = useOptions();
  const { run } = useRequest(getSubjectClassList, {
    manual: true,
    debounceWait: 1000,
    onError: (error) => {
      console.error("获取学科班级列表失败", error);
    },
    onSuccess: (res) => {
      const temp = [];
      for (let i = 0; i < res.subjectToGradeClasses.length; i++) {
        const subObject: {
          subjectKey: number;
          subjectName: string;
          gradeClasses: Array<{
            gradeID: number;
            gradeName: string;
            classID: number;
            className: string;
          }>;
        } = {
          subjectKey: res.subjectToGradeClasses[i].subjectKey,
          subjectName: res.subjectToGradeClasses[i].subjectName,
          gradeClasses: [],
        };
        for (
          let j = 0;
          j < res.subjectToGradeClasses[i].gradeClasses.length;
          j++
        ) {
          for (
            let k = 0;
            k < res.subjectToGradeClasses[i].gradeClasses[j].class.length;
            k++
          ) {
            const cls = {
              gradeID: res.subjectToGradeClasses[i].gradeClasses[j].gradeID,
              gradeName: res.subjectToGradeClasses[i].gradeClasses[j].gradeName,
              classID:
                res.subjectToGradeClasses[i].gradeClasses[j].class[k].classID,
              className:
                res.subjectToGradeClasses[i].gradeClasses[j].class[k].className,
            };
            subObject.gradeClasses.push(cls);
          }
        }
        temp.push(subObject);
      }
      setSubjectClass([...temp]);
      setQuery({ ...query, subject: temp[0].subjectKey });
      runAsk({ subject: temp[0].subjectKey });
    },
  });
  const { loading, run: runAsk } = useRequest(
    async (pageInfo?: {
      subject?: number;
      page?: number;
      pageSize?: number;
    }) => {
      const params: AskDataParams = {
        studentName: query.studentName.trim(),
        courseName: "", //query.courseName.trim(),
        classId: query.classId,
        subjectId:
          pageInfo && pageInfo.subject ? pageInfo.subject : query.subject,
        startTime: Math.floor(query.startDate.valueOf() / 1000),
        endTime: Math.floor(query.endDate.valueOf() / 1000),
        page: pageInfo && pageInfo.page ? pageInfo.page + 1 : query.page + 1,
        pageSize:
          pageInfo && pageInfo.pageSize ? pageInfo.pageSize : query.pageSize,
        sortBy: sort.sortBy,
        sortType: sort.sortType,
      };
      return studentAskDataList(params);
    },
    {
      manual: true,
      refreshDeps: [pageChange],
      debounceWait: 500,
      onSuccess: (data) => {
        if (data) {
          const newData = data.list
            ? data.list.map((item, index) => ({
                ...item,
                id: index,
              }))
            : [];
          setAskData([...newData]);
          setQuery({
            ...query,
            page: data.pageInfo.page - 1,
            pageSize: data.pageInfo.pageSize,
            total: data.pageInfo.total,
          });
          if (data.list) {
            const temp: {
              [key: string]: { question: boolean; answer: boolean };
            } = {};
            data.list.forEach((item, index) => {
              temp[`${item.sendTime}_${item.studentId}_${index}`] = {
                question: false,
                answer: false,
              };
            });
            setAnswerOpen(temp);
          }
        }
      },
      onError: (err) => {
        toast.error("获取数据失败" + err.message);
      },
    }
  );
  const handleBack = () => {
    router.back();
  };
  const handleDateChange = (date: DateRangeValue) => {
    setQuery({
      ...query,
      startDate: dayjs(date[0]).startOf("date").toDate(),
      endDate: dayjs(date[1]).endOf("date").toDate(),
    });
  };
  useMount(() => {
    run();
    //   runAsk();
    // setLoading(true);
  });
  // useUpdateEffect(() => {
  //   runAsk();
  // }, [pageChange]);
  const handleExpandChange = (key: string, type: "question" | "answer") => {
    const temp = { ...answerOpen };
    temp[key][type] = !temp[key][type];
    setAnswerOpen({ ...temp });
  };
  const columns: TableColumn<AskDataResponse["list"][0] & { id: number }>[] =
    useMemo(
      () => [
        {
          title: "提问时间",
          field: "sendTime",
          fixed: "left",
          align: "center",
          width: 5.5 * 10,
          // sortable: true,
          render: (_, record) => {
            return (
              <div className="text-gray-1 w-[120px] text-sm font-normal">
                {dayjs(record.sendTime * 1000).format("MM/DD HH:mm")}
              </div>
            );
          },
        },
        {
          title: "班级",
          field: "className",
          align: "center",
          width: 5.5 * 10,
          render: (_, record) => {
            return (
              <div className="text-gray-1 min-w-[80px] text-sm font-normal">
                {`${record.gradeName}${record.className}`}
              </div>
            );
          },
        },
        {
          title: "姓名",
          field: "studentName",
          align: "center",
          width: 5.5 * 15,
          render: (_, record) => {
            return (
              <div className="text-gray-1 text-sm font-normal">
                {record.studentName}
              </div>
            );
          },
        },
        {
          title: "学科",
          field: "subjectName",
          align: "center",
          width: 5.5 * 8,
          render: (_, record) => {
            return (
              <div className="text-gray-1 min-w-[40px] text-sm font-normal">
                {record.subjectName}
              </div>
            );
          },
        },
        {
          title: "课程名称",
          field: "knowledgeName",
          align: "center",
          width: 5.5 * 10,
          render: (_, record) => {
            return (
              <div className="text-gray-1 min-w-[80px] text-sm font-normal">
                {record.knowledgeName}
              </div>
            );
          },
        },
        {
          title: "学生提问",
          field: "userQuestion",
          align: "center",
          width: 5.5 * 10,
          render: (_, record) => {
            const key = `${record.sendTime}_${record.studentId}_${record.id}`;
            const isOpen = answerOpen[key]?.question || false;

            // 使用自定义 Hook 检测文本是否溢出
            const [ref, isOverflow] = useIsTextOverflow<HTMLDivElement>([
              record.userQuestion,
              isOpen,
            ]);
            return (
              <div className="flex flex-col items-center whitespace-pre-wrap text-left">
                <div
                  ref={ref}
                  className={cn(
                    "text-gray-1 w-[120px] text-sm font-normal transition-all duration-300 ease-in-out",
                    !answerOpen[
                      `${record.sendTime}_${record.studentId}_${record.id}`
                    ]?.question && "line-clamp-2"
                  )}
                >{`${record.userQuestion}`}</div>
                {(isOverflow || isOpen) && (
                  <Button
                    variant="link"
                    className={cn(
                      "text-primary-2 m-0 cursor-pointer gap-1 p-0 !px-0 text-sm font-normal hover:no-underline",
                      answerOpen[
                        `${record.sendTime}_${record.studentId}_${record.id}`
                      ]?.question
                        ? "-ml-[70px]"
                        : "-ml-[44px]"
                    )}
                    onClick={() =>
                      handleExpandChange(
                        `${record.sendTime}_${record.studentId}_${record.id}`,
                        "question"
                      )
                    }
                  >
                    {answerOpen[
                      `${record.sendTime}_${record.studentId}_${record.id}`
                    ]?.question
                      ? "收起"
                      : "查看全部"}
                    {answerOpen[
                      `${record.sendTime}_${record.studentId}_${record.id}`
                    ]?.question ? (
                      <ChevronDown />
                    ) : (
                      <ChevronUp />
                    )}
                  </Button>
                )}
              </div>
            );
          },
        },
        {
          title: "系统回复",
          field: "AIAnswer",
          align: "center",
          width: 5.5 * 22,
          render: (_, record) => {
            const key = `${record.sendTime}_${record.studentId}_${record.id}`;
            const isOpen = answerOpen[key]?.answer || false;

            // 使用自定义 Hook 检测文本是否溢出
            const [ref, isOverflow] = useIsTextOverflow<HTMLDivElement>([
              record.AIAnswer,
              isOpen,
            ]);
            return (
              <div className="text-gray-1 flex flex-col items-center whitespace-pre-wrap text-left text-sm font-normal">
                <div
                  ref={ref}
                  className={cn(
                    "w-[200px] transition-all duration-300 ease-in-out",
                    !answerOpen[
                      `${record.sendTime}_${record.studentId}_${record.id}`
                    ]?.answer && "line-clamp-2"
                  )}
                >{`${record.AIAnswer}`}</div>
                {(isOverflow || isOpen) && (
                  <Button
                    variant="link"
                    className={cn(
                      "text-primary-2 m-0 cursor-pointer gap-1 p-0 !px-0 text-sm font-normal hover:no-underline",
                      answerOpen[
                        `${record.sendTime}_${record.studentId}_${record.id}`
                      ]?.answer
                        ? "-ml-[150px]"
                        : "-ml-[124px]"
                    )}
                    onClick={() =>
                      handleExpandChange(
                        `${record.sendTime}_${record.studentId}_${record.id}`,
                        "answer"
                      )
                    }
                  >
                    {answerOpen[
                      `${record.sendTime}_${record.studentId}_${record.id}`
                    ]?.answer
                      ? "收起"
                      : "查看全部"}
                    {answerOpen[
                      `${record.sendTime}_${record.studentId}_${record.id}`
                    ]?.answer ? (
                      <ChevronDown />
                    ) : (
                      <ChevronUp />
                    )}
                  </Button>
                )}
              </div>
            );
          },
        },
      ],
      [answerOpen]
    );
  const handleSortChange = useCallback(
    (field: string | undefined, direction: "asc" | "desc" | undefined) => {
      // 将字段名映射到API所需的排序字段
      let sortBy: "sendTime" | undefined = undefined;

      if (field) {
        // 根据列字段名映射为API所需的排序字段
        switch (field) {
          case "sendTime":
            sortBy = "sendTime";
            break;
          default:
            sortBy = undefined;
        }
      }

      // 更新排序状态信号
      if (sort.sortBy !== sortBy || sort.sortType !== direction) {
        setSort({ sortBy, sortType: direction });
      }
      console.log(`排序变更: ${sortBy} ${direction}`);
    },
    [setSort]
  );
  const handleSortingChange = useCallback(
    (sortingState: SortingState) => {
      if (sortingState.length === 0) {
        // 没有排序时清空排序状态
        handleSortChange(undefined, undefined);
      } else {
        // 从排序信息中获取字段和方向
        const [{ id, desc }] = sortingState;
        handleSortChange(id, desc ? "desc" : "asc");
      }
    },
    [handleSortChange]
  );
  const customToolbar = useMemo(
    () => (
      <div className="flex w-full items-center justify-between gap-2">
        <div className="flex items-center gap-4">
          <span className="whitespace-nowrap text-sm text-gray-500">
            共 {query.total} 条数据
          </span>
        </div>
      </div>
    ),
    [query]
  );
  const handlePageChange = useCallback(
    (index: number, size?: number) => {
      console.log(index, size);
      setQuery({
        ...query,
        page: index === 0 ? 0 : index - 1,
        pageSize: size ? size : query.pageSize,
      });
      // const pChange = !pageChange;
      // setPageChange(pChange);
      runAsk({
        page: index === 0 ? 0 : index - 1,
        pageSize: size ? size : query.pageSize,
      });
    },
    [query]
  );
  const memoizedDataTable = useMemo(() => {
    return (
      <LazyDataTable
        localKey="ask-report-table"
        // height={"calc(100vh-19rem)"}
        data={askData}
        columns={columns as TableColumn<object>[]}
        showColumnSetting={false}
        rowKey="id"
        // toolbar={customToolbar}
        className="border-none bg-white"
        classNames={{
          container:
            "bg-white relative py-4 px-5 rounded-[0.75rem] border border-line-1",
          tableWrapper: "!h-[calc(100vh-300px)]",
          header: "bg-fill-gray-2", // 移除sticky相关样式，使用属性控制
          headerRow: "border-none ",
          row: "hover:bg-gray-50 transition-colors",
          empty: "text-gray-500 italic",
          loading: "text-primary animate-pulse",
          cell: "text-[0.875rem] text-gray-1 font-normal leading-[150%]",
          pagination: "mt-3",
          toolbar: "mb-0",
        }}
        onSortingChange={handleSortingChange}
        skeletonLoading={loading}
        pagination={{
          page: query.page,
          pageSize: query.pageSize,
          onChange: handlePageChange,
          pageSizeOptions: [10, 20],
          total: query.total,
          numberMode: true,
        }}
      />
    );
  }, [columns, loading, handleSortingChange, handlePageChange, query, askData]);
  const resetField = () => {
    setQuery({
      ...query,
      subject: 0,
      classId: 0,
      studentName: "",
      courseName: "",
      startDate: dayjs().subtract(6, "day").toDate(),
      endDate: dayjs().toDate(),
      page: 0,
    });
  };
  const submitField = () => {
    runAsk({ page: 0, pageSize: query.pageSize });
  };
  const handleStudentChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value;
    setQuery({ ...query, studentName: value });
  };
  const handleCourseChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value;
    setQuery({ ...query, courseName: value });
  };
  //   console.log("class", subjectClass);
  return (
    <div>
      <PageHeader needBack onBack={handleBack}>
        <div className="text-gray-1 ml-4 text-xl font-semibold">问一问</div>
      </PageHeader>
      <div className="gap-4">
        <div className="mb-4 grid grid-cols-2 gap-4 px-6">
          <div className="flex items-center">
            <span className="inline-block w-14 text-base font-normal">
              学生
            </span>
            <Input
              placeholder="搜索姓名"
              value={query.studentName}
              onChange={handleStudentChange}
              className="h-8 bg-white"
            />
          </div>
          {/* <div className="flex items-center">
            <span className="w-15 inline-block text-base font-normal">
              课程
            </span>
            <Input
              placeholder="搜索课程名称"
              onChange={handleCourseChange}
              className="h-8 bg-white"
            />
          </div> */}
          <div className="flex items-center">
            <span className="inline-block w-12 text-base font-normal">
              日期
            </span>
            <DatePicker
              icon={(isOpen) => (
                <ChevronDown
                  className={cn(
                    "h-4 w-4 transition-transform duration-300",
                    isOpen.value
                      ? "text-primary-1 rotate-[-180deg]"
                      : "text-gray-2 rotate-[0deg]"
                  )}
                />
              )}
              value={[
                query.startDate ? query.startDate : undefined,
                query.endDate ? query.endDate : undefined,
              ]}
              mode="range"
              max={30}
              onClick={() => {
                // DONE: 埋点10 => `homework_list_time_click` 作业列表中点切换时间
                // umeng.trackEvent(
                //   UmengCategory.HOMEWORK,
                //   UmengHomeworkAction.TIME_CLICK,
                //   {}
                // );
              }}
              onConfirm={handleDateChange}
              format="yyyy.MM.dd"
              className="border-line-1 text-gray-2 data-[slot=popover-trigger]:data-[state=open]:border-primary-1 data-[slot=popover-trigger]:data-[state=open]:text-primary-1 border-input h-8 flex-1 overflow-hidden text-ellipsis whitespace-nowrap rounded-[0.375rem] border bg-transparent bg-white font-[500] shadow-none transition-colors hover:bg-[#f5f7fa] data-[slot=popover-trigger]:data-[state=open]:bg-white"
              textBtnClassName="flex-1 overflow-hidden whitespace-nowrap text-ellipsis"
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 px-6">
          <div className="flex items-center">
            <span className="inline-block w-12 text-base font-normal">
              学科
            </span>
            <Select
              value={String(query.subject)}
              onValueChange={(value) => {
                setQuery({ ...query, subject: Number(value) });
              }}
              onOpenChange={setSubjectOpen}
              open={subjectOpen}
            >
              <SelectTrigger
                className="flex-1 cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap border-none p-0 shadow-none"
                classNames={{
                  icon: `!hidden`,
                }}
              >
                <TriggerInput
                  className="border-input h-8 flex-1 overflow-hidden text-ellipsis"
                  isOpen={subjectOpen}
                  onClick={() => {
                    // DONE: 埋点8 => `homework_list_subject_click` 作业列表中点切换学科
                    // umeng.trackEvent(
                    //   UmengCategory.HOMEWORK,
                    //   UmengHomeworkAction.SUBJECT_CLICK,
                    //   {}
                    // );
                  }}
                >
                  <SelectValue placeholder={"选择学科"} />
                </TriggerInput>
              </SelectTrigger>
              <SelectContent>
                {/* <SelectItem key={0} value={"0"}>
                  全部学科
                </SelectItem> */}
                {(subjectClass || []).map((subject) => (
                  <SelectItem
                    key={subject.subjectKey}
                    value={String(subject.subjectKey)}
                  >
                    {subject.subjectName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center">
            <span className="inline-block w-12 text-base font-normal">
              班级
            </span>
            <Select
              value={String(query.classId)}
              onValueChange={(value) => {
                setQuery({ ...query, classId: Number(value) });
              }}
              onOpenChange={setClassOpen}
              open={classOpen}
            >
              <SelectTrigger
                className="flex-1 cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap border-none p-0 shadow-none"
                classNames={{
                  icon: `!hidden`,
                }}
              >
                <TriggerInput
                  className="border-input h-8 flex-1 overflow-hidden text-ellipsis"
                  isOpen={classOpen}
                  onClick={() => {
                    // DONE: 埋点8 => `homework_list_subject_click` 作业列表中点切换学科
                    // umeng.trackEvent(
                    //   UmengCategory.HOMEWORK,
                    //   UmengHomeworkAction.SUBJECT_CLICK,
                    //   {}
                    // );
                  }}
                >
                  <SelectValue placeholder={"选择班级"} />
                </TriggerInput>
              </SelectTrigger>
              <SelectContent>
                <SelectItem key={0} value={"0"}>
                  全部班级
                </SelectItem>
                {(
                  subjectClass.find((sub) => sub.subjectKey === query.subject)
                    ?.gradeClasses || []
                ).map((cls) => (
                  <SelectItem key={cls.classID} value={String(cls.classID)}>
                    {`${cls.gradeName + cls.className}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {/* <div className="flex items-center">
            <span className="inline-block w-12 text-base font-normal">
              日期
            </span>
            <DatePicker
              icon={(isOpen) => (
                <ChevronDown
                  className={cn(
                    "h-4 w-4 transition-transform duration-300",
                    isOpen.value
                      ? "text-primary-1 rotate-[-180deg]"
                      : "text-gray-2 rotate-[0deg]"
                  )}
                />
              )}
              value={[
                query.startDate ? query.startDate : undefined,
                query.endDate ? query.endDate : undefined,
              ]}
              mode="range"
              max={30}
              onClick={() => {
                // DONE: 埋点10 => `homework_list_time_click` 作业列表中点切换时间
                // umeng.trackEvent(
                //   UmengCategory.HOMEWORK,
                //   UmengHomeworkAction.TIME_CLICK,
                //   {}
                // );
              }}
              onConfirm={handleDateChange}
              format="yyyy.MM.dd"
              className="border-line-1 text-gray-2 data-[slot=popover-trigger]:data-[state=open]:border-primary-1 data-[slot=popover-trigger]:data-[state=open]:text-primary-1 border-input h-8 flex-1 overflow-hidden text-ellipsis whitespace-nowrap rounded-[0.375rem] border bg-transparent bg-white font-[500] shadow-none transition-colors hover:bg-[#f5f7fa] data-[slot=popover-trigger]:data-[state=open]:bg-white"
              textBtnClassName="flex-1 overflow-hidden whitespace-nowrap text-ellipsis"
            />
          </div> */}
        </div>
      </div>
      <div className="mt-4 flex items-center justify-end px-6">
        {/* <span className="text-primary-1 whitespace-nowrap text-sm font-normal">
          共 {query.total} 条数据
        </span> */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={resetField}
            className="rounded-full font-normal"
          >
            重置
          </Button>
          <Button
            className="text-primary-2 ml-2 rounded-full border border-[#6574fc] bg-white font-normal hover:text-white"
            size="sm"
            onClick={submitField}
          >
            确定
          </Button>
        </div>
      </div>
      <div className="px-6 py-2">{memoizedDataTable}</div>
    </div>
  );
}
