import { FileErrorCode } from "@/enums";
import UploadDropArea from "@/public/icons/ic_upload_drop_area.svg";
import { Input } from "@/ui/input";
import { Progress } from "@/ui/progress";
import { getFileIcon, getFileNameAndExtension } from "@/utils/file";
import { UppyFile } from "@uppy/core";
import { UppyContext, useDropzone, useUppyState } from "@uppy/react";
import bytes from "bytes";
import { useContext } from "react";
import { match, P } from "ts-pattern";

type TchUppyFile = UppyFile<
  {
    error?: { code: FileErrorCode; message: string };
    vodPercentage: number;
    vodToken?: object;
  },
  {}
>;

export function Dashboard() {
  const { uppy } = useContext(UppyContext);
  const files = useUppyState(uppy!, (state) => state.files);
  const fileArray = Object.values(files || {}) as TchUppyFile[];

  const { getInputProps, getRootProps } = useDropzone({
    onFileInputChange: () => uppy?.clear(),
  });

  const accept = (uppy?.opts?.restrictions?.allowedFileTypes || []).join(", ");

  return (
    <div className="flex w-full flex-col items-center overflow-hidden px-6">
      <div {...getRootProps()} className="cursor-pointer">
        <UploadDropArea />

        {/* 文件输入框 */}
        <input
          {...getInputProps()}
          multiple={false}
          accept={accept}
          style={{ display: "none" }}
        />
      </div>

      {fileArray.map((file) => {
        const [filename] = getFileNameAndExtension(file.name || "");
        const ossPercentage = file.progress.percentage || 0;
        const vodPercentage = Number(file.meta.vodPercentage) || 0;

        const percentage = file.meta.vodToken
          ? Math.ceil((vodPercentage + ossPercentage) / 2)
          : ossPercentage;

        const FileIconComponent = getFileIcon(file.extension);

        return (
          <div
            key={file.id}
            className="border-line-2 mt-3 w-full overflow-hidden rounded-lg border p-3"
          >
            <div key={file.id} className="flex items-center overflow-hidden">
              {/* 文件图标 */}
              <span className="flex-shrink-0">
                <FileIconComponent className="h-6 w-6" />
              </span>

              <div className="flex min-w-0 flex-1 flex-col overflow-hidden pl-2.5 pr-3">
                <div className="text-gray-1 truncate text-sm/normal">
                  {file.meta.name}
                </div>

                <div className="h-4.5 flex items-center">
                  {match([file.error, percentage])
                    .with([P.string, P._], () => (
                      <span className="text-danger-1 text-xxs/normal mr-1 flex-none">
                        {file.meta.error?.message || "上传失败"}
                      </span>
                    ))
                    .with([P.not(P.string), P.when((v) => v < 100)], () => (
                      <Progress
                        value={percentage}
                        max={100}
                        className="> div:bg-primary-2 bg-fill-gray-1 rounded-xs h-1"
                      />
                    ))
                    .with([P.not(P.string), P.when((v) => v >= 100)], () => (
                      <div className="text-gray-4 text-xs/normal">
                        {bytes(file.size || 0)}
                      </div>
                    ))
                    .otherwise(() => null)}
                </div>
              </div>

              {/* 重试按钮 */}
              {file.error &&
                file.meta.error?.code !== FileErrorCode.FileCorrupted && (
                  <span
                    className="text-primary-1 mr-3 cursor-pointer text-xs"
                    onClick={() => {
                      uppy?.setFileMeta(file.id, { error: undefined });
                      uppy?.retryUpload(file.id);
                    }}
                  >
                    重试
                  </span>
                )}

              {/* 删除按钮 */}
              {percentage < 100 && (
                <button
                  onClick={() => uppy?.removeFile(file.id)}
                  className="flex-shrink-0 cursor-pointer rounded-full p-1 text-red-500 transition-colors hover:bg-red-50 hover:text-red-700"
                  title="删除文件"
                >
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              )}
            </div>

            {/* 修改文件名 */}
            {(file.meta.error?.code === FileErrorCode.FilenameTooLong ||
              file.meta.error?.code === FileErrorCode.FilenameDuplicate) && (
              <div className="w-full pt-2">
                <div className="text-gray-2 text-sm/loose">文件名</div>
                <Input
                  defaultValue={filename}
                  placeholder="请输入新的文件名"
                  className="ring-0! h-8 w-full"
                  onBlur={(e) => {
                    const value = e.target.value.trim();

                    if (!value) {
                      return;
                    }

                    uppy?.setFileState(file.id, {
                      name: `${value}.${file.extension}`,
                    });
                  }}
                />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
