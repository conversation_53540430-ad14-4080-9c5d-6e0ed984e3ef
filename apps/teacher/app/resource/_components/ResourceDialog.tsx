"use client";
import CloseIcon from "@/public/icons/ic_close.svg";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/ui/tch-dialog";

export default function ResourceDialog({
  title,
  open,
  setOpen,
  children,
}: {
  title: string;
  open: boolean;
  setOpen: (open: boolean) => void;
  children: React.ReactNode;
}) {
  return (
    <Dialog open={open}>
      <DialogContent
        className="w-130 max-h-full !max-w-[unset] grid-rows-[auto_1fr] gap-0 rounded-2xl p-0 outline-0"
        close={false}
      >
        <DialogHeader className="p-6">
          <DialogTitle className="text-gray-1 flex items-start justify-between text-sm font-semibold not-italic">
            <div className="flex-1">{title}</div>
            <CloseIcon
              className="size-5 cursor-pointer active:opacity-40"
              onClick={() => {
                setOpen(false);
              }}
            />
          </DialogTitle>
          <DialogDescription className="sr-only">
            资源管理对话框
          </DialogDescription>
        </DialogHeader>

        {children}
      </DialogContent>
    </Dialog>
  );
}
