"use client";

import { GET_GRADE_AND_SUBJECT_LIST_REQUEST_KEY } from "@/configs";
import { getGradeAndSubjectList, updateResource } from "@/services";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/form";
import { Input } from "@/ui/input";
import { Button } from "@/ui/tch-button";
import TchSelect from "@/ui/tch-select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCreation, useRequest } from "ahooks";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 表单验证模式
const formSchema = z.object({
  userFileName: z
    .string()
    .transform((val) => val.trim()) // 先 trim
    .refine((val) => val.length > 0, "请输入文件名"),
  grade: z.array(z.number()).optional(),
  subject: z.array(z.number()).optional(),
});

type FormData = z.infer<typeof formSchema>;

export default function EditResource({
  onSuccess,
  onCancel,
  defaultValues,
  teacherResourceDbId,
  fileName,
}: {
  onSuccess: () => void;
  onCancel: () => void;
  defaultValues?: Partial<FormData>;
  teacherResourceDbId: number;
  fileName: string;
}) {
  const getGradeAndSubjectListRequest = useRequest(getGradeAndSubjectList, {
    cacheKey: GET_GRADE_AND_SUBJECT_LIST_REQUEST_KEY,
    staleTime: -1,
  });

  // 年级选项 - 直接转换为 TchSelect 需要的格式
  const gradeOptions = useCreation(() => {
    const grades =
      getGradeAndSubjectListRequest.data?.phraseGrade?.map((item) => {
        return item.grades;
      }) ?? [];

    return grades.flat().map((item) => ({
      value: item.id,
      label: item.name,
    }));
  }, [getGradeAndSubjectListRequest.data]);

  // 学科选项 - 直接转换为 TchSelect 需要的格式
  const subjectOptions = useCreation(() => {
    const subjects = getGradeAndSubjectListRequest.data?.subjects || [];
    return subjects.map((item) => ({
      value: item.id,
      label: item.name,
    }));
  }, [getGradeAndSubjectListRequest.data]);

  const form = useForm<FormData>({
    // @ts-expect-error TODO zodResolver重载方法，识别有问题
    resolver: zodResolver(formSchema),
    defaultValues: {
      userFileName: defaultValues?.userFileName || fileName.split(".")[0],
      grade: defaultValues?.grade || [],
      subject: defaultValues?.subject || [],
    },
  });

  // 更新资源
  const updateResourceRequest = useRequest(
    () => {
      // 获取表单数据
      const data = form.getValues();
      const ext = fileName.split(".").pop();

      return updateResource({
        teacherResourceDbId,
        userFileName: data.userFileName.trim() + (ext ? `.${ext}` : ""),
        grade: data.grade ? data.grade.sort() : [],
        subject: data.subject ?? [],
      });
    },
    {
      manual: true,
      debounceWait: 500,
      onSuccess,
    }
  );

  return (
    <div>
      <div className="w-full px-6">
        <div className="mb-6">
          <p className="text-sm text-gray-600">请为上传的文件选择合适的标签</p>
        </div>

        <Form {...form}>
          <FormField
            control={form.control}
            name="userFileName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700">
                  文件名
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入文件名"
                    className="h-8 w-full"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="grade"
            render={({ field }) => {
              return (
                <FormItem className="mt-6">
                  <FormLabel className="text-sm font-medium text-gray-700">
                    适用年级
                  </FormLabel>
                  <FormControl>
                    <TchSelect
                      isMulti
                      options={gradeOptions}
                      value={gradeOptions.filter((option) =>
                        field.value?.includes(option.value)
                      )}
                      onChange={(selectedOptions) => {
                        field.onChange(
                          selectedOptions.map((option) => option.value)
                        );
                      }}
                      placeholder="请选择年级"
                      classNames={{
                        control: () => "rounded-md border-input min-h-9",
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />

          <FormField
            control={form.control}
            name="subject"
            render={({ field }) => {
              const selectedSubjectOption = subjectOptions.find(
                (item) => item.value === field.value?.[0]
              );

              return (
                <FormItem className="mt-6">
                  <FormLabel className="text-sm font-medium text-gray-700">
                    适用学科
                  </FormLabel>
                  <FormControl>
                    <TchSelect
                      isMulti={false}
                      options={subjectOptions}
                      value={selectedSubjectOption ?? null}
                      onChange={(selectedOption) => {
                        const value = selectedOption
                          ? [selectedOption.value]
                          : [];
                        field.onChange(value);
                      }}
                      placeholder="请选择学科"
                      className="w-full"
                      classNames={{
                        control: () => "rounded-md border-input min-h-9",
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        </Form>

        <div className="pt-13.5 flex justify-end gap-3 pb-5">
          <Button
            type="default"
            size="lg"
            radius="full"
            className="feedback_btn_cancel text-gray-2 min-w-24"
            onClick={onCancel}
          >
            取 消
          </Button>
          <Button
            type="primary"
            size="lg"
            radius="full"
            className="feedback_btn_submit min-w-24"
            onClick={async () => {
              const isValid = await form.trigger();

              if (!isValid) {
                return;
              }

              updateResourceRequest.run();
            }}
          >
            确定
          </Button>
        </div>
      </div>
    </div>
  );
}
