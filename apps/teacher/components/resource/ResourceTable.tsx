"use client";

import TableEmpty from "@/components/TableEmpty";
import IcMore from "@/public/icons/ic_more.svg";
import { getResourceList } from "@/services";
import { ResourceItem } from "@/types/resource";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/dropdown-menu";
import { ScrollArea } from "@/ui/scroll-area";
import Spin from "@/ui/spin";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationNumbers,
  PaginationPrevious,
} from "@/ui/tch-pagination";
import { cn } from "@/utils";
import { getFileIcon } from "@/utils/file";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useControllableValue, useRequest } from "ahooks";
import dayjs from "dayjs";
import { produce } from "immer";
import * as React from "react";
import { match } from "ts-pattern";
import ResourcePreviewer from "./ResourcePreviewer";

export function ResourceTable({
  pageSize = 10,
  searchKeyword = "",
  selectedGradeList,
  selectedSubjectList,
  joinedResources,
  setJoinedResources,
  needOperation = false,
  ref,
  ...props
}: ResourceTableProps) {
  const [rowSelection, setRowSelection] = React.useState({});
  const [currentPage, setCurrentPage] = useControllableValue(props, {
    defaultValue: 1,
    valuePropName: "currentPage",
    trigger: "setCurrentPage",
  });

  // 获取资源列表
  const resourceListRequest = useRequest(
    () => {
      const resourceListParams = {
        page: currentPage,
        pageSize: pageSize,
        resourceName: searchKeyword || undefined,
        grade: selectedGradeList?.map((item) => item.id) ?? [],
        subject: selectedSubjectList?.map((item) => item.id) ?? [],
      };

      return getResourceList(resourceListParams);
    },
    {
      refreshDeps: [
        currentPage,
        pageSize,
        searchKeyword,
        selectedGradeList,
        selectedSubjectList,
      ],
      debounceWait: 300, // 搜索防抖
      debounceLeading: true,
    }
  );

  React.useImperativeHandle(ref, () => {
    return {
      refresh: resourceListRequest.refresh,
    };
  });

  const totalPages = resourceListRequest.data?.total
    ? Math.ceil(resourceListRequest.data.total / pageSize)
    : 0;

  // 数据和加载状态
  const rows = resourceListRequest.data?.resources || [];
  const loading = resourceListRequest.loading;

  // 当前要操作的数据
  const [currentResource, setCurrentResource] =
    React.useState<ResourceItem | null>(null);
  const [previewOpen, setPreviewOpen] = React.useState(false);

  // 创建表格实例
  const table = useReactTable({
    data: rows,
    columns: [
      // {
      //   id: "select",
      //   header: ({ table }) => (
      //     <Checkbox
      //       checked={table.getIsAllPageRowsSelected()}
      //       onCheckedChange={(value) =>
      //         table.toggleAllPageRowsSelected(!!value)
      //       }
      //       aria-label="Select all"
      //       className="cursor-pointer"
      //     />
      //   ),
      //   cell: ({ row }) => (
      //     <Checkbox
      //       checked={row.getIsSelected()}
      //       onCheckedChange={(value) => row.toggleSelected(!!value)}
      //       aria-label="Select row"
      //       className="cursor-pointer"
      //     />
      //   ),
      //   enableHiding: false,
      //   meta: {
      //     headerClassName: "w-12",
      //     cellClassName: "w-12",
      //   },
      // },
      {
        accessorKey: "userFilename",
        header: "名称",
        cell: ({ row }) => {
          const resource = row.original;
          const FileIcon = getFileIcon(resource.fileExtension);
          return (
            <div className="flex min-w-0 items-center gap-3">
              <FileIcon className="h-6 w-6 flex-shrink-0" />
              <span className="text-gray-1 truncate font-medium">
                {resource.userFilename}.{resource.fileExtension}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: "grade",
        header: "年级",
        cell: ({ getValue }) => (
          <div className="text-gray-2 truncate text-sm">
            {getValue<string>()}
          </div>
        ),
        meta: {
          headerClassName: "w-[12%]",
          cellClassName: "w-[12%]",
        },
      },
      {
        accessorKey: "subject",
        header: "学科",
        cell: ({ getValue }) => (
          <div className="text-gray-2 truncate text-sm">
            {getValue<string>()}
          </div>
        ),
        meta: {
          headerClassName: "w-[12%]",
          cellClassName: "w-[12%]",
        },
      },
      {
        accessorKey: "updatedAt",
        header: "更新日期",
        cell: ({ getValue }) => {
          const dateValue = getValue<number>();

          const formattedDate = dateValue
            ? dayjs.unix(dateValue).format("YYYY-MM-DD HH:mm")
            : "";
          return <div className="text-gray-2 text-sm">{formattedDate}</div>;
        },
        meta: {
          headerClassName: "w-40",
          cellClassName: "w-40",
        },
      },
      {
        id: "actions",
        header: "操作",
        cell: ({ row }) => {
          const resource = row.original;

          return (
            <div className="flex items-center gap-4">
              <button
                onClick={() => {
                  setCurrentResource(resource);
                  setPreviewOpen(true);
                }}
                className="text-primary-1 hover:text-primary-2 cursor-pointer text-sm font-medium transition-colors"
              >
                打开
              </button>

              {match(
                joinedResources.findIndex(
                  (item) =>
                    item.teacherResourceDbId === resource.teacherResourceDbId
                )
              )
                .when(
                  (index) => index > -1,
                  (index) => (
                    <button
                      onClick={() => {
                        setJoinedResources(
                          produce(joinedResources, (draft) => {
                            draft.splice(index, 1);
                          })
                        );
                      }}
                      className="text-danger-1 hover:text-danger-1 cursor-pointer text-sm font-medium transition-colors"
                    >
                      取消加入
                    </button>
                  )
                )
                .otherwise(() => {
                  return (
                    <button
                      onClick={() => {
                        setJoinedResources(
                          produce(joinedResources, (draft) => {
                            draft.push(resource);
                          })
                        );
                      }}
                      className="text-primary-1 hover:text-primary-2 cursor-pointer text-sm font-medium transition-colors"
                    >
                      加入
                    </button>
                  );
                })}

              {needOperation && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="cursor-pointer rounded p-1 outline-0 transition-colors hover:bg-gray-100">
                      <IcMore className="h-4 w-4" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-32">
                    <DropdownMenuItem
                      onClick={() => {
                        props.onEdit?.(resource);
                      }}
                      className="focus:bg-primary-6 text-gray-2 focus:text-gray-2 cursor-pointer text-sm"
                    >
                      编辑
                    </DropdownMenuItem>

                    <DropdownMenuItem
                      onClick={() => {
                        props.onDelete?.(resource);
                      }}
                      className="focus:bg-primary-6 text-gray-2 focus:text-gray-2 cursor-pointer text-sm"
                    >
                      删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          );
        },
        meta: {
          headerClassName: "w-40",
          cellClassName: "w-40",
        },
      },
    ],
    state: {
      rowSelection,
    },
    onRowSelectionChange: (value) => {
      setRowSelection(value);
    },
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: true,
    manualPagination: true,
    pageCount: totalPages,
  });

  return (
    <Spin loading={loading} className="flex h-full flex-col overflow-hidden">
      {/* 固定表头 */}
      <div className="flex-none">
        <Table className="table-fixed text-sm">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow
                key={headerGroup.id}
                className="bg-fill-gray-2 hover:bg-fill-gray-2 border-0! w-full"
              >
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(
                        header.column.columnDef.meta?.headerClassName,
                        "text-gray-2 h-12 px-4 text-sm font-medium"
                      )}
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
        </Table>
      </div>

      {/* 可滚动表体 */}
      <ScrollArea className="flex-1 overflow-hidden">
        <Table className="w-full table-fixed text-sm">
          <TableBody>
            {match(table.getRowModel().rows.length)
              .when(
                (length) => length > 0,
                () =>
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      className="text-gray-1 hover:bg-fill-gray-2/50 data-[state=selected]:bg-primary-6/30 border-line-2 h-16 border-b transition-colors last:border-b-0"
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          className={cn(
                            cell.column.columnDef.meta?.cellClassName,
                            "px-4 py-3"
                          )}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
              )
              .otherwise(() => (
                <TableRow>
                  <TableCell
                    colSpan={table.getAllColumns().length}
                    className="p-0 text-center hover:bg-white"
                  >
                    {!loading && <TableEmpty className="bg-white" />}
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </ScrollArea>

      {/* 分页组件 */}
      {totalPages > 0 && (
        <Pagination className="flex-none justify-end pt-2">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage > 1) {
                    setCurrentPage(currentPage - 1);
                  }
                }}
                className={
                  currentPage <= 1 ? "pointer-events-none opacity-50" : ""
                }
              />
            </PaginationItem>

            <PaginationNumbers
              currentPage={currentPage}
              totalPages={totalPages}
              onPageClick={(page) => setCurrentPage(page)}
            />

            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage < totalPages) {
                    setCurrentPage(currentPage + 1);
                  }
                }}
                className={
                  currentPage >= totalPages
                    ? "pointer-events-none opacity-50"
                    : ""
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}

      {/* 资源预览组件 */}
      {currentResource && previewOpen && (
        <ResourcePreviewer
          visible={previewOpen}
          onClose={() => {
            setPreviewOpen(false);
            setCurrentResource(null);
          }}
          resourceIds={[currentResource.resourceId]}
        />
      )}
    </Spin>
  );
}

// 扩展 ColumnMeta 类型
declare module "@tanstack/react-table" {
  interface ColumnMeta<TData, TValue> {
    headerClassName?: string;
    cellClassName?: string;
  }
}

export interface ResourceTableRef {
  // 刷新资源列表
  refresh: () => void;
}

interface ResourceTableProps {
  ref?: React.Ref<ResourceTableRef>;
  // 是不是需要编辑和删除等功能
  needOperation?: boolean;
  resetParams?: () => void;
  currentPage?: number;
  setCurrentPage?: (page: number) => void;
  pageSize?: number;
  searchKeyword?: string;
  selectedGradeList?: readonly { id: number; name: string }[];
  selectedSubjectList?: readonly { id: number; name: string }[];

  onDelete?: (resource: ResourceItem) => void;
  onEdit?: (resource: ResourceItem) => void;
  // 已加入的资源列表
  joinedResources: ResourceItem[];
  setJoinedResources: (resources: ResourceItem[]) => void;
}
