export * from './baseTree';
export * from './bizTree';
export * from './comment';
export * from './question';
export * from './questionInput';
export * from './questionLib';
export * from './resource';
import useEnumManagerMap from '@/hooks/useEnumManager';
import { transformBizTreeListToTreeStructure } from '@/utils';
import { useRequest } from '@umijs/max';
import { fetchBaseTreeDetail, fetchBaseTreeList } from './baseTree';
import { fetchBizTreeDetail, fetchBizTreeList } from './bizTree';

/**
 *
 * @param condition true 基础树列表，false 章节树列表
 * @param params [phaseId 学段, subjectId 学科]
 * @returns
 */
export function useFetchTreeList(condition: boolean, params: number[]) {
  const { materialList } = useEnumManagerMap();
  const { data, run, error } = useRequest(
    async () => {
      const [phaseId, subjectId] = params;
      if (!phaseId && !subjectId) {
        return {
          data: {
            list: [],
            formatList: [],
          },
        };
      }
      const payload = {
        phaseList: phaseId ? [phaseId] : [],
        subjectList: subjectId ? [subjectId] : [],
        page: 1,
        pageSize: 10000,
      };
      const response = condition
        ? await fetchBaseTreeList(payload as API.BaseTreeListRequestParams)
        : await fetchBizTreeList(payload as API.BizTreeListRequestParams);

      if (condition) {
        const treeList = (response as API.BaseTreeListResponse).data.list.map(
          (item: API.BaseTreeItem) => {
            return {
              label: item.baseTreeName,
              value: item.baseTreeId,
            };
          },
        );
        (response as API.BaseTreeListResponse).data.formatList = treeList;
        return response;
      } else {
        const bizTreeList = (response as API.BizTreeListResponse).data.list.map(
          (item: API.BizTreeItem) => {
            return {
              label: item.bizTreeName,
              value: item.bizTreeId,
              material: item.material,
            };
          },
        );
        (response as API.BizTreeListResponse).data.formatList = transformBizTreeListToTreeStructure(
          bizTreeList,
          materialList,
        );
        return response;
      }
    },
    {
      manual: true,
      debounceInterval: 100,
      refreshDeps: [condition, params],
    },
  );
  return { data, run, error };
}

/**
 * 统一处理基础树和业务树的详情数据
 * @param condition true 基础树详情，false 业务树详情
 * @param treeId 树ID
 * @returns 统一后的树节点数据
 */
export function useFetchTreeDetail(
  condition: boolean,
  treeId: number,
  sceneCategory: number = 0,
  showShelfStatus: boolean = false,
): { data: any; run: () => void; error: Error | undefined; mutate: (data: any) => void } {
  const formatTreeNode = (
    node: API.BaseTreeNode | API.BizTreeNode,
    parentsKey: number[] = [],
  ): API.Common.BaseTreeNodeAntdType => {
    const antdNode: API.Common.BaseTreeNodeAntdType =
      'baseTreeNodeId' in node
        ? {
            key: node.baseTreeNodeId,
            title: node.baseTreeNodeName,
            children: undefined,
            parentsKey,
          }
        : {
            key: node.bizTreeNodeId,
            title: node.bizTreeNodeName,
            parentsKey,
            children: undefined,
            shelfStatus: node.shelfStatus,
            shelfOnLeafNodeStat: node.shelfOnLeafNodeStat,
          };
    if ('baseTreeNodeChildren' in node && node.baseTreeNodeChildren.length > 0) {
      antdNode.children = node.baseTreeNodeChildren.map((child: API.BaseTreeNode) =>
        formatTreeNode(child, [...parentsKey, antdNode.key]),
      );
    } else if ('bizTreeNodeChildren' in node && node.bizTreeNodeChildren.length > 0) {
      antdNode.children = node.bizTreeNodeChildren.map((child: API.BizTreeNode) =>
        formatTreeNode(child, [...parentsKey, antdNode.key]),
      );
    }

    return antdNode;
  };

  const { data, run, error, mutate } = useRequest(
    async () => {
      if (!treeId) return null;

      const response = condition
        ? await fetchBaseTreeDetail(treeId)
        : await fetchBizTreeDetail(treeId, sceneCategory, showShelfStatus ? 1 : 0);

      if (condition) {
        const baseResponse = response as API.BaseTreeDetailResponse;
        if (baseResponse.data?.baseTreeDetail) {
          const treeData = [formatTreeNode(baseResponse.data.baseTreeDetail)];
          baseResponse.data.treeData = treeData;

          return baseResponse;
        }
      } else {
        const bizResponse = response as API.BizTreeDetailResponse;
        if (bizResponse.data?.bizTreeDetail) {
          const treeData = [formatTreeNode(bizResponse.data.bizTreeDetail)];
          bizResponse.data.treeData = treeData;
          return bizResponse;
        }
      }
      return [];
    },
    {
      manual: true,
      refreshDeps: [condition, treeId],
      onSuccess: () => {
        // console.log('====== detial res: ', res);
      },
    },
  );

  return { data, run, error, mutate };
}
