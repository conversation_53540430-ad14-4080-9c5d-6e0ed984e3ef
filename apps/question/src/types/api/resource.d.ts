// import { ResourceStatus } from '@/utils/resource';

declare namespace API {
  namespace Resource {
    /**
     * 审核任务列表项
     */
    interface ListItem {
      resourceId: number; // 资源id
      originFile: string; // 资源名称
      creater: string; // 提交人
      createTime: number; // 提交时间（时间戳）
      createTimeStr: string; // 提交时间
      resourceStatus: ResourceStatus; // 资源状态
      resourceUrl: string; // 资源地址
      resourceType: string; // 资源类型
    }

    interface ListSearchValues {
      originFile?: string; // 名称
      resourceStatus?: ResourceStatus;
      creater?: string; // 提交人
      createTime?: string; // 提交时间

      current: number; // 页码
      pageSize: number; // 每页条数
    }
    interface ListRequestParams {
      resourceStatus?: ResourceStatus;
      originFile?: string;
      createrName?: string;
      createTime?: string;

      page: number; // 页码
      pageSize: number; // 每页条数
    }
  }
}
