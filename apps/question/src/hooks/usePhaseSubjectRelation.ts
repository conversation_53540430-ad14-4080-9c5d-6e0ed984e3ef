import { useModel } from '@umijs/max';
import { useMemo } from 'react';

/**
 * 获取学段科目关联关系
 */
export const usePhaseSubjectRelation = () => {
  const { initialState } = useModel('@@initialState');
  const phaseSubjectRelation = useMemo(
    () => initialState?.globalDictValues?.enumConstants?.phaseSubjectRelation ?? [],
    [initialState],
  );

  const phaseSubjectTreeOptions = useMemo(() => {
    return phaseSubjectRelation.map((phase) => ({
      label: phase.nameZh,
      value: phase.value,
      children: phase.subjectList.map((subject) => ({
        label: subject.nameZh,
        value: subject.value,
      })),
    }));
  }, [phaseSubjectRelation]);
  return {
    phaseSubjectRelation,
    phaseSubjectTreeOptions,
  };
};
