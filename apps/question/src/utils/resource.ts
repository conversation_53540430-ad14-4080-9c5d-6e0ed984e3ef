export enum ResourceStatus {
  UPLOAD_SUCCESS = 1, // 上传成功
  DELETED = 2, // 已删除
}

export const ResourceStatusOptions = [
  {
    label: '上传成功',
    value: ResourceStatus.UPLOAD_SUCCESS,
  },
  {
    label: '已删除',
    value: ResourceStatus.DELETED,
  },
];

export const ResourceEnumValue = {
  [ResourceStatus.UPLOAD_SUCCESS]: {
    text: '上传成功',
    status: 'success',
    value: ResourceStatus.UPLOAD_SUCCESS,
  },
  [ResourceStatus.DELETED]: {
    text: '已删除',
    status: 'error',
    value: ResourceStatus.DELETED,
  },
};

interface FilenameParts {
  filename: string;
  ext: string;
  filenameWithExt: string;
}

export function getFilenameFromUrl(url: string): FilenameParts {
  // 处理URL中可能包含的查询参数和哈希
  let pathname: string;
  try {
    pathname = new URL(url).pathname;
  } catch (e) {
    // 如果URL解析失败（可能是相对路径），则直接使用整个输入作为路径
    pathname = url;
  }

  // 从路径中提取最后一部分
  const filenameWithExt = decodeURIComponent(pathname.split('/').pop() || '');

  // 分离文件名和扩展名
  const lastDotIndex = filenameWithExt.lastIndexOf('.');

  if (lastDotIndex === -1 || lastDotIndex === 0) {
    // 没有扩展名或点号在开头的情况（如 .htaccess）
    return {
      filename: filenameWithExt,
      ext: '',
      filenameWithExt,
    };
  }

  return {
    filename: filenameWithExt.substring(0, lastDotIndex),
    ext: filenameWithExt.substring(lastDotIndex + 1),
    filenameWithExt,
  };
}
