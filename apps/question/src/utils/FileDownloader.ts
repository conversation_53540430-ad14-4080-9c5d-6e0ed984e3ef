type ProgressCallback = (
  progress: number,
  downloaded: number,
  total: number,
  speed: number,
) => void;
/**
 * 文件下载工具类
 * 支持任意格式文件下载，限制最大文件大小为500M，并提供下载进度跟踪
 */
export class FileDownloader {
  // 最大文件大小限制：500MB（字节）
  private static MAX_FILE_SIZE = 500 * 1024 * 1024;

  /**
   * 进度回调函数类型定义
   * @param progress 下载进度百分比 (0-100)
   * @param downloaded 已下载字节数
   * @param total 总字节数（如果已知）
   * @param speed 下载速度（字节/秒）
   */

  /**
   * 从URL下载文件，支持进度跟踪
   * @param url 文件URL
   * @param fileName 可选的文件名
   * @param onProgress 进度回调函数
   * @returns Promise<void>
   */
  static async downloadFromUrl(
    url: string,
    fileName?: string,
    onProgress?: ProgressCallback,
  ): Promise<void> {
    console.log('下载文件:', url);
    try {
      // 先发送HEAD请求获取文件大小
      const headResponse = await fetch(url, { method: 'HEAD' });

      if (!headResponse.ok) {
        throw new Error(`无法获取文件信息，状态码: ${headResponse.status}`);
      }

      // 获取文件大小
      const contentLength = headResponse.headers.get('content-length');
      let fileSize = 0;

      if (contentLength) {
        fileSize = parseInt(contentLength, 10);

        // 检查文件大小是否超过限制
        if (fileSize > this.MAX_FILE_SIZE) {
          throw new Error(`文件过大，最大支持${this.formatFileSize(this.MAX_FILE_SIZE)}`);
        }
      }

      // 下载文件，使用流处理以支持进度跟踪
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`下载失败，状态码: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('响应不包含可读取的内容');
      }

      // 确定文件名
      const finalFileName = fileName || this.getFileNameFromUrl(url) || 'download';

      // 使用ReadableStream处理下载进度
      await this.downloadWithProgress(response.body, fileSize, finalFileName, onProgress);
    } catch (error) {
      console.error('下载失败:', error);
      throw error;
    }
  }

  /**
   * 通过流下载并跟踪进度
   * @param readableStream 可读流
   * @param totalSize 总文件大小
   * @param fileName 文件名
   * @param onProgress 进度回调函数
   */
  private static async downloadWithProgress(
    readableStream: ReadableStream,
    totalSize: number,
    fileName: string,
    onProgress?: ProgressCallback,
  ): Promise<void> {
    const reader = readableStream.getReader();
    const chunks: Uint8Array[] = [];
    let downloadedSize = 0;
    let startTime = Date.now();
    let lastTime = startTime;
    let lastDownloaded = 0;

    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      if (value) {
        chunks.push(value);
        downloadedSize += value.length;

        // 计算下载速度（每500ms更新一次）
        const currentTime = Date.now();
        let speed = 0;

        if (currentTime - lastTime > 500) {
          const timeDiff = (currentTime - lastTime) / 1000; // 转换为秒
          const downloadedDiff = downloadedSize - lastDownloaded;
          speed = downloadedDiff / timeDiff;

          lastTime = currentTime;
          lastDownloaded = downloadedSize;
        }

        // 计算进度并调用回调
        if (onProgress) {
          let progress = 0;
          if (totalSize > 0) {
            progress = Math.min((downloadedSize / totalSize) * 100, 100);
          }
          onProgress(progress, downloadedSize, totalSize, speed);
        }
      }
    }

    // 检查总大小是否超过限制（如果无法通过HEAD请求获取）
    if (downloadedSize > this.MAX_FILE_SIZE) {
      throw new Error(`文件过大，最大支持${this.formatFileSize(this.MAX_FILE_SIZE)}`);
    }

    // 合并所有块并创建Blob
    const blob = new Blob(chunks);

    // 执行下载
    this.downloadBlob(blob, fileName);

    // 最后一次进度更新，确保显示100%
    if (onProgress && totalSize > 0) {
      onProgress(100, downloadedSize, totalSize, 0);
    }
  }

  /**
   * 下载Blob对象
   * @param blob Blob对象
   * @param fileName 文件名
   */
  static downloadBlob(blob: Blob, fileName: string): void {
    // 检查Blob大小
    if (blob.size > this.MAX_FILE_SIZE) {
      throw new Error(`文件过大，最大支持${this.formatFileSize(this.MAX_FILE_SIZE)}`);
    }

    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;

    // 模拟点击下载
    document.body.appendChild(a);
    a.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 100);
  }

  /**
   * 从URL中提取文件名
   * @param url 文件URL
   * @returns 文件名
   */
  private static getFileNameFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const fileName = pathname.split('/').pop();

      return fileName || null;
    } catch (e) {
      return null;
    }
  }

  /**
   * 格式化文件大小显示
   * @param bytes 字节数
   * @returns 格式化后的字符串
   */
  static formatFileSize(bytes: number): string {
    if (bytes >= 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    } else if (bytes >= 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    }
    return `${bytes} B`;
  }

  /**
   * 格式化下载速度显示
   * @param bytesPerSecond 每秒字节数
   * @returns 格式化后的字符串
   */
  static formatSpeed(bytesPerSecond: number): string {
    if (bytesPerSecond >= 1024 * 1024) {
      return `${(bytesPerSecond / (1024 * 1024)).toFixed(2)} MB/s`;
    } else if (bytesPerSecond >= 1024) {
      return `${(bytesPerSecond / 1024).toFixed(2)} KB/s`;
    }
    return `${bytesPerSecond} B/s`;
  }
}

// 使用示例
// FileDownloader.downloadFromUrl(
//   'https://example.com/large-file.zip',
//   'document.zip',
//   (progress, downloaded, total, speed) => {
//     console.log(
//       `进度: ${progress.toFixed(2)}% | ` +
//       `已下载: ${FileDownloader.formatFileSize(downloaded)} / ${FileDownloader.formatFileSize(total)} | ` +
//       `速度: ${FileDownloader.formatSpeed(speed)}`
//     );
//     // 在UI中更新进度条
//     // document.getElementById('progress-bar')!.style.width = `${progress}%`;
//     // document.getElementById('progress-text')!.textContent = `${progress.toFixed(1)}%`;
//   }
// ).then(() => {
//   console.log('下载完成');
// }).catch(err => {
//   console.error('下载失败:', err.message);
// });
