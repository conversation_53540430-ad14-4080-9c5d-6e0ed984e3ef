import { fetchResourceList } from '@/services/api';
import { getFilenameFromUrl, removeNullFields, ResourceEnumValue } from '@/utils';
import { FileDownloader } from '@/utils/FileDownloader';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useRef, useState } from 'react';

const formatRequestParams = (params: API.Resource.ListSearchValues) => {
  const { resourceStatus, originFile, creater, createTime, current, pageSize } = params;

  let payload: API.Resource.ListRequestParams = {
    page: current!,
    pageSize,
    resourceStatus: parseInt(resourceStatus) || undefined,
    originFile,
    createrName: creater,
    createTime,
  };
  return removeNullFields(payload) as API.Resource.ListRequestParams;
};

const ResourceListPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();

  const actionRef = useRef<ActionType>(null);
  const [reviewItem, setReviewItem] = useState<API.Resource.ListItem | null>(null);
  const [fileLoadingMap, setFileLoadingMap] = useState<Record<number, boolean>>({});

  const downloadResource = (record: API.Resource.ListItem) => {
    if (fileLoadingMap[record.resourceId]) {
      return;
    }

    try {
      setFileLoadingMap((prev) => ({
        ...prev,
        [record.resourceId]: true,
      }));
      const { filenameWithExt } = getFilenameFromUrl(record.resourceUrl);
      let fileName = filenameWithExt;
      if (!fileName) {
        fileName = record.originFile;
      }
      FileDownloader.downloadFromUrl(
        record.resourceUrl,
        fileName,
        (progress: number, downloaded: number, total: number, speed: number) => {
          // console.log(
          //   `进度: ${progress.toFixed(2)}% | ` +
          //     `已下载: ${FileDownloader.formatFileSize(downloaded)} / ${FileDownloader.formatFileSize(total)} | ` +
          //     `速度: ${FileDownloader.formatSpeed(speed)}`,
          // );
          setFileLoadingMap((prev) => ({
            ...prev,
            [record.resourceId]: progress < 100,
          }));
        },
      )
        .then(() => {
          messageApi.success('下载成功');
        })
        .catch((err) => {
          console.error('下载失败:', err.message);
          messageApi.error('下载失败');
        })
        .finally(() => {
          setFileLoadingMap((prev) => ({
            ...prev,
            [record.resourceId]: false,
          }));
        });
    } catch (error) {
      messageApi.error('下载失败');
    }
  };

  const columns: ProColumns<API.Resource.ListItem>[] = [
    {
      title: '资源ID',
      dataIndex: 'resourceId',
      width: 150,
      fieldProps: {
        placeholder: '请输入任务ID',
      },
      search: false,
    },
    {
      title: '资源名称',
      dataIndex: 'originFile',
      ellipsis: true,
      search: true,
      order: 4,
    },

    {
      title: '上传人',
      order: 2,
      dataIndex: 'creater',
      width: 100,
      search: true,
    },
    {
      title: '上传时间',
      dataIndex: 'createTimeStr',
      width: 200,
      search: false,
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      width: 200,
      search: true,
      hideInTable: true,
      valueType: 'date',
      order: 1,
    },

    {
      title: '资源状态',
      order: 3,
      dataIndex: 'resourceStatus',
      width: 130,
      valueType: 'select',
      fieldProps: {
        placeholder: '请选择资源状态',
      },
      valueEnum: ResourceEnumValue,
      search: true,
    },

    {
      title: '操作',
      width: 70,
      valueType: 'option',
      search: false,
      align: 'center',
      render: (_, record) => [
        // <Button
        //   color="primary"
        //   size="small"
        //   variant="text"
        //   key="review1"
        //   onClick={() => setReviewItem(record)}
        // >
        //   查看
        // </Button>,
        fileLoadingMap[record.resourceId] ? (
          <Button color="primary" size="small" variant="text" key="review2">
            下载中...
          </Button>
        ) : (
          <Button
            color="primary"
            size="small"
            variant="text"
            key="review2"
            onClick={() => downloadResource(record)}
          >
            下载
          </Button>
        ),
      ],
    },
  ];

  return (
    <>
      <PageContainer>
        <ProTable<API.Resource.ListItem>
          columns={columns}
          actionRef={actionRef}
          cardBordered
          request={async (params = {}) => {
            const payload = formatRequestParams(params as API.Resource.ListSearchValues);

            try {
              const res = await fetchResourceList(payload);

              return {
                data: res.data.list || [],
                success: res.code === 0,
                total: res.data.total || 0,
              };
            } catch (error) {
              messageApi.error('获取资源列表失败');
              console.log('获取资源列表失败: ', error);
              return {
                data: [],
                success: false,
                total: 0,
              };
            }
          }}
          rowKey="resourceId"
          search={{
            labelWidth: 'auto',
            defaultCollapsed: false,
            span: {
              xs: 24,
              sm: 12,
              md: 8,
              lg: 8,
              xl: 8,
              xxl: 6,
            },
          }}
          options={{
            reload: true,
            density: true,
            setting: true,
          }}
          form={{
            colon: false,
          }}
          pagination={{
            defaultPageSize: 10,
          }}
          tableLayout="fixed"
          dateFormatter="string"
          headerTitle="资源列表"
        />
      </PageContainer>
      {contextHolder}
    </>
  );
};

export default ResourceListPage;
