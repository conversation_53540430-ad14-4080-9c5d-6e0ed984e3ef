"use client";
import { <PERSON><PERSON> } from "@/app/components/common/button";
import { useGuideContext } from "@/app/context/guide-context";
import { post } from "@/lib/fetcher";
import { GuideStatus, GuideTaskType } from "@/types/base";
import { InteractiveData } from "@/types/interactive";
import { CheckCircle, Save } from "lucide-react";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import { GenerateInteractiveButton } from "../interactive-manager/generate-interactive-button";
import { InteractiveManager } from "../interactive-manager/interactive-manager";
import { Panel } from "./panel";

export const InteractivePanel = () => {
  const { guide, refresh } = useGuideContext();
  const {
    flowRunType,
    guideWidgetStatus,
    baseWidgetList,
    guideWidgetId,
    guideWidgetSetId,
  } = guide;
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [interactiveList, setInteractiveList] = useState<InteractiveData[]>([]);

  const isLoading = useMemo(() => {
    return (
      guideWidgetStatus === GuideStatus.Loading &&
      flowRunType === GuideTaskType.GenerateInteractive
    );
  }, [flowRunType, guideWidgetStatus]);

  // 保存排序到后端
  const handleSaveOrder = async () => {
    if (!guideWidgetId || !guideWidgetSetId) {
      toast.error("缺少必要的ID信息");
      return;
    }

    setIsSaving(true);
    try {
      // 准备要发送到后端的数据 - 只需要组件ID的顺序数组
      const baseWidgetOrder = interactiveList.map((item) => item.id);

      // 调用后端API保存排序
      await post("/api/v1/guideWidget/update/baseWidget/interactive/order", {
        arg: {
          guideWidgetSetId,
          guideWidgetId,
          baseWidgetOrder,
        },
      });

      setHasUnsavedChanges(false);
      toast.success("排序已保存到服务器", {
        icon: <CheckCircle className="size-5 text-green-500" />,
      });

      // 刷新数据
      refresh?.();
    } catch (error) {
      console.error("保存排序失败:", error);
      toast.error("保存失败，请重试");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Panel
      title="5.互动组件"
      subtitle="管理和配置互动组件，支持上传/下载JS文件、预览和排序"
      controls={
        <>
          <GenerateInteractiveButton />
          <Button
            type="outline"
            disabled={!hasUnsavedChanges || isSaving}
            loading={isSaving}
            onClick={handleSaveOrder}
            className="rounded-sm text-xs"
            icon={<Save className="size-4" />}
          >
            {isSaving ? "保存中..." : "保存排序"}
          </Button>
        </>
      }
      loading={isLoading}
      className="w-full"
    >
      <InteractiveManager
        onOrderChange={setHasUnsavedChanges}
        onListChange={setInteractiveList}
        baseWidgetList={baseWidgetList}
        guideWidgetId={guideWidgetId}
        guideWidgetSetId={guideWidgetSetId}
        onRefresh={refresh}
      />
    </Panel>
  );
};
