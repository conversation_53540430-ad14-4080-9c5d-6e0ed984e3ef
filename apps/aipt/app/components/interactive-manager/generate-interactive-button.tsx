"use client";

import { But<PERSON> } from "@/app/components/common/button";
import { useGuideContext } from "@/app/context/guide-context";
import { post } from "@/app/utils/fetcher";
import InteractiveIcon from "@/public/interactive.svg";
import { GuideStatus, GuideTaskType } from "@/types/base";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@repo/ui/components/tooltip";
import { useCallback } from "react";
import useSWRMutation from "swr/mutation";

/**
 * AI生成互动组件按钮 - 参考生成图片按钮实现
 */
export const GenerateInteractiveButton = () => {
  const { guide, isInit, isGenerating, isFinish, refresh } = useGuideContext();
  const { videoGenTime, baseWidgetList, guideWidgetStatus, flowRunType } =
    guide;

  // 从 guide 中获取 genInteractiveButtonActive 状态
  const genInteractiveButtonActive = guide.genInteractiveButtonActive;

  // 检查是否已达到最大数量限制（10个）
  const isMaxComponentsReached = baseWidgetList && baseWidgetList.length >= 10;

  // 使用SWR处理接口调用 - 参考生成图片按钮的实现
  const { trigger, isMutating } = useSWRMutation(
    "/api/v1/guideWidget/produce/baseWidget/interactive",
    post
  );

  // 检查是否正在生成互动组件
  const isGeneratingInteractive =
    guideWidgetStatus === GuideStatus.Loading &&
    flowRunType === GuideTaskType.GenerateInteractive;

  // 判断按钮是否可用：视频已生成完成且不在生成中，且后端允许点击，且未保存完整段落，且未达到最大数量
  const isDisabled =
    isInit ||
    isGenerating || // 任何生成任务进行时都禁用
    isFinish ||
    !videoGenTime ||
    genInteractiveButtonActive === 0 ||
    isMaxComponentsReached; // 后端控制：0不可点，1可点

  const handleGenerate = useCallback(async () => {
    const { guideWidgetId, guideWidgetSetId } = guide;

    // 检查前置条件
    if (guide.genInteractiveButtonActive === 0) {
      return;
    }

    try {
      // 调用AI生成互动组件接口
      await trigger({
        guideWidgetId,
        guideWidgetSetId,
      });

      // 立即刷新数据以获取最新状态（这会触发全局 isGenerating 状态）
      refresh?.();
    } catch (error) {
      console.error("AI生成互动组件接口调用失败:", error);
    }
  }, [guide, refresh, trigger]);

  // 如果达到上限，使用Tooltip包装按钮
  if (isMaxComponentsReached) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="inline-block">
            <Button
              type="outline"
              disabled={isDisabled}
              loading={isMutating || isGeneratingInteractive}
              onClick={handleGenerate}
              className="rounded-sm text-xs"
              icon={<InteractiveIcon className="size-4 fill-current" />}
            >
              生成互动组件
            </Button>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>已达到上限10个互动组件</p>
        </TooltipContent>
      </Tooltip>
    );
  }

  // 未达到上限时，直接返回按钮
  return (
    <Button
      type="outline"
      disabled={isDisabled}
      loading={isMutating || isGeneratingInteractive}
      onClick={handleGenerate}
      className="rounded-sm text-xs"
      icon={<InteractiveIcon className="size-4 fill-current" />}
    >
      生成互动组件
    </Button>
  );
};
