"use client";
import { InteractiveContentView } from "@/app/(pages)/lesson-flow/_components/interactive/interactive-content-view";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetD<PERSON><PERSON>,
  Sheet<PERSON>ooter,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON>itle,
} from "@/app/components/ui/sheet";
import footer from "@/public/interactive/interactive-box-footer.png";
import header from "@/public/interactive/interactive-box-header.png";
import { Download } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface InteractivePreviewModalProps {
  open: boolean;
  onClose: () => void;
  url: string;
  typeName: string;
}

export const InteractivePreviewModal = ({
  open,
  onClose,
  url,
  typeName,
}: InteractivePreviewModalProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const handleReport = (e: CustomEvent<unknown>) => {
    // 处理互动组件报告事件
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleError = (error: unknown) => {
    console.error("Interactive component load error:", error);
    setIsLoading(false);
    setHasError(true);
  };

  // 下载JS文件
  const handleDownload = async () => {
    // 防止重复下载
    if (isDownloading) {
      toast.warning("下载正在进行中，请稍候...");
      return;
    }

    if (!url) {
      toast.error("没有找到可下载的JS文件");
      return;
    }

    setIsDownloading(true);

    try {
      // 获取文件内容
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status}`);
      }

      const content = await response.text();

      // 创建下载链接
      const blob = new Blob([content], { type: "application/javascript" });
      const downloadUrl = URL.createObjectURL(blob);

      // 从URL中提取文件名，如果没有则使用默认名称
      const urlParts = url.split("/");
      const fileName =
        urlParts[urlParts.length - 1] ||
        `${typeName || "interactive-component"}.js`;

      // 创建临时下载链接
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(downloadUrl);

      toast.success("JS文件下载成功");
    } catch (error) {
      console.error("下载JS文件失败:", error);
      toast.error("下载失败，请检查网络连接或文件是否存在");
    } finally {
      // 延迟重置状态，避免用户快速重复点击
      setTimeout(() => {
        setIsDownloading(false);
      }, 1000);
    }
  };

  // 检查是否是真实的URL（包括blob URL）
  const isRealUrl = url && (url.startsWith("http") || url.startsWith("blob:"));

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className="w-[1000px] !max-w-none gap-2 bg-gray-50">
        <SheetHeader className="gap-0 pb-0">
          <div className="flex items-center justify-between">
            <SheetTitle>互动预览</SheetTitle>
          </div>
          <SheetDescription></SheetDescription>
        </SheetHeader>

        {/* 预览内容区域 - 复用lesson-flow的InteractiveContentView */}
        <div className="relative h-[600px] w-[960px] overflow-y-auto px-4">
          {isRealUrl ? (
            <InteractiveContentView
              url={url}
              type={typeName}
              onReport={handleReport}
            />
          ) : (
            // 空组件时使用相同的布局结构
            <div className="py-22 mx-auto h-full w-[82%]">
              <div className="relative h-full w-full p-2.5">
                {/* 顶部装饰图片 */}
                <div
                  className="z-1 absolute -top-[38px] left-0 h-[64px] w-[410px] bg-cover bg-center bg-no-repeat"
                  style={{ backgroundImage: `url(${header.src})` }}
                ></div>

                {/* 底部装饰图片 */}
                <div
                  className="z-1 absolute -bottom-[29px] right-0 h-[56px] w-[390px] bg-cover bg-center bg-no-repeat"
                  style={{ backgroundImage: `url(${footer.src})` }}
                ></div>

                {/* 背景 */}
                <div className="absolute left-0 top-0 z-0 h-full w-full rounded-[20px] bg-[#4F4761]"></div>

                {/* 空组件提示 */}
                <div className="border-3 z-3 relative h-full w-full overflow-auto rounded-[20px] border-[#B7CDFF]">
                  <div className="flex h-full items-center justify-center">
                    <div className="text-center">
                      <div className="mb-4 text-lg font-medium text-white">
                        {!url && !typeName ? "空互动组件" : "暂无互动组件"}
                      </div>
                      <div className="text-sm text-gray-300">
                        {!url && !typeName
                          ? "这是一个空组件，请上传JS文件来添加内容"
                          : "请先上传或生成互动组件"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 错误状态覆盖层 */}
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <div className="text-center">
                <div className="mb-4 text-lg font-medium text-red-400">
                  组件加载失败
                </div>
                <div className="text-sm text-gray-300">
                  请检查JS文件是否正确
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 下载按钮 - 移到底部按钮区域 */}

        <SheetFooter>
          <div className="flex items-center justify-end gap-2">
            {url && !url.includes("example.com") && (
              <Button
                variant="outline"
                onClick={handleDownload}
                disabled={isDownloading}
                className="flex items-center gap-2"
              >
                <Download
                  size={16}
                  className={isDownloading ? "animate-spin" : ""}
                />
                {isDownloading ? "下载中..." : "下载JS文件"}
              </Button>
            )}
            {(!url || !typeName) && (
              <div className="text-sm text-gray-500">
                空组件无法下载，请先上传JS文件
              </div>
            )}
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
