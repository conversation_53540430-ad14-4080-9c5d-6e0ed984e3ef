"use client";
import { post } from "@/lib/fetcher";
import { InteractiveData } from "@/types/interactive";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface UseInteractiveActionsProps {
  guideWidgetId?: number;
  guideWidgetSetId?: number;
  onRefresh?: () => void;
  interactiveList: InteractiveData[];
  setInteractiveList: React.Dispatch<React.SetStateAction<InteractiveData[]>>;
}

/**
 * 互动组件操作自定义 Hook
 * 处理删除、预览、复制URL等操作
 */
export const useInteractiveActions = ({
  guideWidgetId,
  guideWidgetSetId,
  onRefresh,
  interactiveList,
  setInteractiveList,
}: UseInteractiveActionsProps) => {
  const [previewModal, setPreviewModal] = useState<{
    open: boolean;
    data: InteractiveData | null;
  }>({ open: false, data: null });

  const [deleteModal, setDeleteModal] = useState<{
    open: boolean;
    data: InteractiveData | null;
  }>({ open: false, data: null });

  const handlePreview = useCallback((item: InteractiveData) => {
    setPreviewModal({ open: true, data: item });
  }, []);

  const handleClosePreview = useCallback(() => {
    setPreviewModal({ open: false, data: null });
  }, []);

  const handleDelete = useCallback(
    (id: string) => {
      const item = interactiveList.find((item) => item.id === id);
      if (item) {
        setDeleteModal({ open: true, data: item });
      }
    },
    [interactiveList]
  );

  const handleCloseDelete = useCallback(() => {
    setDeleteModal({ open: false, data: null });
  }, []);

  const confirmDelete = useCallback(async () => {
    const item = deleteModal.data;
    if (!item) return;

    const itemName = item.widgetName || `互动${item.widgetIndex}`;

    try {
      if (guideWidgetId && guideWidgetSetId) {
        await post("/api/v1/guideWidget/del/baseWidget/interactive", {
          arg: {
            guideWidgetSetId,
            guideWidgetId,
            baseWidgetId: item.id,
          },
        });

        onRefresh?.();
        toast.success(`${itemName}删除成功`);
      } else {
        // 兼容旧逻辑
        const newList = interactiveList.filter((i) => i.id !== item.id);
        const updatedList = newList.map((item, index) => ({
          ...item,
          index,
          widgetIndex: index + 1,
        }));
        setInteractiveList(updatedList);
        toast.success(`${itemName}删除成功`);
      }
    } catch (error) {
      console.error("删除互动组件失败:", error);
      const errorMessage = error instanceof Error ? error.message : "未知错误";
      toast.error(`删除失败: ${errorMessage}`);
    } finally {
      setDeleteModal({ open: false, data: null });
    }
  }, [
    deleteModal.data,
    guideWidgetId,
    guideWidgetSetId,
    onRefresh,
    interactiveList,
    setInteractiveList,
  ]);

  const handleCopyUrl = useCallback(async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success("URL已复制到剪贴板");
    } catch (error) {
      console.error("复制失败:", error);
      const errorMessage = error instanceof Error ? error.message : "未知错误";
      toast.error(`复制失败: ${errorMessage}`);
    }
  }, []);

  return {
    previewModal,
    deleteModal,
    handlePreview,
    handleClosePreview,
    handleDelete,
    handleCloseDelete,
    confirmDelete,
    handleCopyUrl,
  };
};
