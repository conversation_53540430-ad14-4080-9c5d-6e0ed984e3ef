"use client";
import { InteractiveData } from "@/types/interactive";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface UseInteractiveDragDropProps {
  interactiveList: InteractiveData[];
  setInteractiveList: React.Dispatch<React.SetStateAction<InteractiveData[]>>;
  setRenderKey: React.Dispatch<React.SetStateAction<number>>;
  onOrderChange?: (hasChanges: boolean) => void;
}

/**
 * 互动组件拖拽排序自定义 Hook
 * 处理拖拽开始、拖拽中、拖拽结束等逻辑
 */
export const useInteractiveDragDrop = ({
  interactiveList,
  setInteractiveList,
  setRenderKey,
  onOrderChange,
}: UseInteractiveDragDropProps) => {
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  const handleDragStart = useCallback((e: React.DragEvent, id: string) => {
    setDraggedItem(id);
    e.dataTransfer.effectAllowed = "move";
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setDragOverIndex(index);
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragOverIndex(null);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent, dropIndex: number) => {
      e.preventDefault();

      if (!draggedItem) return;

      const draggedIndex = interactiveList.findIndex(
        (item) => item.id === draggedItem
      );
      if (draggedIndex === -1 || draggedIndex === dropIndex) {
        setDraggedItem(null);
        setDragOverIndex(null);
        return;
      }

      const newList = [...interactiveList];
      const [draggedItemData] = newList.splice(draggedIndex, 1);
      if (draggedItemData) {
        newList.splice(dropIndex, 0, draggedItemData);
      }

      const updatedList = newList.map((item, index) => ({
        ...item,
        index,
        widgetIndex: index + 1,
      }));

      setInteractiveList(updatedList);
      setDraggedItem(null);
      setDragOverIndex(null);

      // 强制重新渲染所有组件以解决拖拽后预览空白的问题
      setRenderKey((prev) => prev + 1);

      // 通知父组件有未保存的更改
      onOrderChange?.(true);

      toast.success("排序已更新，请点击保存按钮同步到服务器");
    },
    [
      draggedItem,
      interactiveList,
      setInteractiveList,
      setRenderKey,
      onOrderChange,
    ]
  );

  const handleDragEnd = useCallback(() => {
    setDraggedItem(null);
    setDragOverIndex(null);
  }, []);

  return {
    draggedItem,
    dragOverIndex,
    handleDragStart,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleDragEnd,
  };
};
