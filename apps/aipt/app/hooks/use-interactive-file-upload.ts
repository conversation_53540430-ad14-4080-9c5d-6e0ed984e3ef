"use client";
import { extractComponentName } from "@/app/utils/interactive-component-utils";
import { post } from "@/lib/fetcher";
import { uploadFile } from "@/lib/uploader";
import {
  FILE_UPLOAD_CONFIG,
  formatFileSize,
  InteractiveData,
} from "@/types/interactive";
import { useCallback } from "react";
import { toast } from "sonner";

interface UseInteractiveFileUploadProps {
  guideWidgetId?: number;
  guideWidgetSetId?: number;
  onRefresh?: () => void;
  interactiveList: InteractiveData[];
  setInteractiveList: React.Dispatch<React.SetStateAction<InteractiveData[]>>;
}

/**
 * 互动组件文件上传自定义 Hook
 * 处理文件上传、验证、API调用等逻辑
 */
export const useInteractiveFileUpload = ({
  guideWidgetId,
  guideWidgetSetId,
  onRefresh,
  interactiveList,
  setInteractiveList,
}: UseInteractiveFileUploadProps) => {
  const handleFileUpload = useCallback(
    async (file: File) => {
      try {
        // 检查文件类型
        if (!file.name.endsWith(".js")) {
          toast.error("请上传.js文件");
          return;
        }

        // 检查文件大小
        if (file.size > FILE_UPLOAD_CONFIG.MAX_FILE_SIZE) {
          toast.error(
            `文件大小不能超过 ${formatFileSize(FILE_UPLOAD_CONFIG.MAX_FILE_SIZE)}，当前文件大小：${formatFileSize(file.size)}`
          );
          return;
        }

        // 显示上传中状态
        toast.loading("正在上传JS文件...", { id: "upload-js" });

        const typeName = await extractComponentName(file);
        const { url, fileName } = await uploadFile(file, "2");

        // 调用添加互动组件的API
        if (guideWidgetId && guideWidgetSetId) {
          await post("/api/v1/guideWidget/add/baseWidget/interactive", {
            arg: {
              guideWidgetSetId,
              guideWidgetId,
              data: { url, typeName },
            },
          });

          onRefresh?.();
          toast.success(`互动组件添加成功: ${typeName}`, { id: "upload-js" });
        } else {
          // 兼容旧逻辑
          const newComponent: InteractiveData = {
            id: Date.now().toString(),
            url,
            typeName,
            index: interactiveList.length,
            widgetIndex: interactiveList.length + 1,
            fileName,
            sourceType: "upload",
          };

          setInteractiveList([...interactiveList, newComponent]);
          toast.success(`互动组件上传成功: ${typeName}`, { id: "upload-js" });
        }
      } catch (error) {
        console.error("文件上传错误:", error);
        const errorMessage =
          error instanceof Error ? error.message : "未知错误";
        toast.error(`文件上传失败: ${errorMessage}`, { id: "upload-js" });
      }
    },
    [
      guideWidgetId,
      guideWidgetSetId,
      onRefresh,
      interactiveList,
      setInteractiveList,
    ]
  );

  const handleFileReplace = useCallback(
    async (id: string, file: File) => {
      try {
        toast.loading("正在替换JS文件...", { id: "replace-js" });

        const typeName = await extractComponentName(file);
        const { url, fileName } = await uploadFile(file, "2");

        if (guideWidgetId && guideWidgetSetId) {
          await post("/api/v1/guideWidget/save/baseWidget/interactive", {
            arg: {
              guideWidgetSetId,
              guideWidgetId,
              baseWidgetId: id,
              data: { url, typeName },
            },
          });

          onRefresh?.();
          toast.success(`组件替换成功: ${typeName}`, { id: "replace-js" });
        } else {
          // 兼容旧逻辑
          setInteractiveList((prev: InteractiveData[]) =>
            prev.map((item: InteractiveData) =>
              item.id === id
                ? { ...item, url, typeName, fileName, sourceType: "upload" }
                : item
            )
          );
          toast.success(`组件替换成功: ${typeName}`, { id: "replace-js" });
        }
      } catch (error) {
        console.error("文件替换错误:", error);
        const errorMessage =
          error instanceof Error ? error.message : "未知错误";
        toast.error(`文件替换失败: ${errorMessage}`, { id: "replace-js" });
      }
    },
    [guideWidgetId, guideWidgetSetId, onRefresh, setInteractiveList]
  );

  const handleCreateEmpty = useCallback(async () => {
    try {
      if (guideWidgetId && guideWidgetSetId) {
        await post("/api/v1/guideWidget/add/baseWidget/interactive", {
          arg: {
            guideWidgetSetId,
            guideWidgetId,
            data: { url: "", typeName: "" },
          },
        });

        onRefresh?.();
        toast.success("空互动组件创建成功，请上传JS文件");
      } else {
        // 兼容旧逻辑
        const newComponent: InteractiveData = {
          id: Date.now().toString(),
          url: "",
          typeName: "",
          index: interactiveList.length,
          widgetIndex: interactiveList.length + 1,
          widgetName: `互动${interactiveList.length + 1}`,
          sourceType: "upload",
        };

        setInteractiveList([...interactiveList, newComponent]);
        toast.success("空互动组件创建成功，请上传JS文件");
      }
    } catch (error) {
      console.error("创建空互动组件失败:", error);
      const errorMessage = error instanceof Error ? error.message : "未知错误";
      toast.error(`创建失败: ${errorMessage}`);
    }
  }, [
    guideWidgetId,
    guideWidgetSetId,
    onRefresh,
    interactiveList,
    setInteractiveList,
  ]);

  return {
    handleFileUpload,
    handleFileReplace,
    handleCreateEmpty,
  };
};
