import { Line, LineTexture } from "@repo/core/types/data/widget-guide";

/**
 * H3分组信息
 */
export interface H3Group {
  ols: Array<{
    line: Line;
    characterCount: number;
    order: number;
  }>;
}

/**
 * H4分组信息
 */
export interface H4Group {
  h4Line: Line;
  h4Title: string;
  ols: Array<{
    line: Line;
    characterCount: number;
    order: number;
  }>;
}

/**
 * 分组样式判断结果
 */
export interface GroupStyleResult {
  groupType: "h3" | "h4";
  groupIndex: number;
  canUseStyle: boolean;
  styleToApply: 0 | 1 | 2 | 3;
  reason: string;
  h4Title?: string;
  olLines?: Line[]; // 需要应用样式的OL行
}

/**
 * 有序列表分析结果
 */
export interface OLAnalysisResult {
  hasOL: boolean;
  h3Groups: H3Group[];
  h4Groups: H4Group[];
}

/**
 * 文字排版判断结果
 */
export interface TextLayoutResult {
  canUseTextLayout: boolean;
  groupResults: GroupStyleResult[];
  overallReason: string;
  hasAnyApplicableGroup: boolean;
}

/**
 * 简单计算数学公式的等效字符数
 * 使用简化算法，避免过度复杂化
 */
function calculateMathFormulaWeight(formula: string): number {
  // 简单策略：数学公式按原长度的70%计算（经验值）
  return Math.ceil(formula.length * 0.7);
}

/**
 * 计算单行内容的字符数（考虑数学公式）
 */
function calculateLineCharacterCount(line: Line): number {
  if (!line.content || !Array.isArray(line.content)) {
    return 0;
  }

  if (line.content.length > 0) {
    const firstItem = line.content[0];
    if (firstItem && typeof firstItem === "object" && "content" in firstItem) {
      const htmlContent = (line.content as LineTexture[])
        .map((texture) => texture.content)
        .join("");

      let adjustedContent = htmlContent;

      // 处理数学公式：简化算法
      // 行内公式：\(...\) 和 $...$
      adjustedContent = adjustedContent.replace(
        /\\\((.*?)\\\)/g,
        (_, formula) => {
          return "x".repeat(Math.max(1, calculateMathFormulaWeight(formula)));
        }
      );
      adjustedContent = adjustedContent.replace(
        /\$([^$]*?)\$/g,
        (_, formula) => {
          return "x".repeat(Math.max(1, calculateMathFormulaWeight(formula)));
        }
      );

      // 块级公式：\[...\] 和 $$...$$
      adjustedContent = adjustedContent.replace(
        /\\\[([\s\S]*?)\\\]/g,
        (_, formula) => {
          return "x".repeat(Math.max(1, calculateMathFormulaWeight(formula)));
        }
      );
      adjustedContent = adjustedContent.replace(
        /\$\$([\s\S]*?)\$\$/g,
        (_, formula) => {
          return "x".repeat(Math.max(1, calculateMathFormulaWeight(formula)));
        }
      );

      // 移除HTML标签，只计算实际文本
      const textContent = adjustedContent.replace(/<[^>]*>/g, "");
      return textContent.length;
    }
  }

  return 0;
}

/**
 * 提取H4标题文本
 */
function extractH4Title(h4Line: Line): string {
  if (!h4Line.content || !Array.isArray(h4Line.content)) {
    return "未知标题";
  }

  const title = (h4Line.content as LineTexture[])
    .map((texture) => texture.content)
    .join("")
    .replace(/<[^>]*>/g, "")
    .trim();

  return title || "未知标题";
}

/**
 * 分析H3部分的有序列表结构（按分组）
 */
export function analyzeOLStructure(lines: Line[]): OLAnalysisResult {
  const result: OLAnalysisResult = {
    hasOL: false,
    h3Groups: [],
    h4Groups: [],
  };

  const groups = {
    currentH3Group: null as H3Group | null,
    currentH4Group: null as H4Group | null,
    currentLevel: undefined as number | undefined,
  };

  function processLines(lineList: Line[]) {
    lineList.forEach((line) => {
      if (line.tag === "h3") {
        // 遇到H3，结束当前H4分组，开始新的H3分组
        if (groups.currentH4Group) {
          result.h4Groups.push(groups.currentH4Group);
          groups.currentH4Group = null;
        }
        groups.currentH3Group = { ols: [] };
        groups.currentLevel = 3;
      } else if (line.tag === "h4") {
        // 遇到H4，结束当前H3分组，开始新的H4分组
        if (groups.currentH3Group && groups.currentH3Group.ols.length > 0) {
          result.h3Groups.push(groups.currentH3Group);
          groups.currentH3Group = null;
        }
        if (groups.currentH4Group) {
          result.h4Groups.push(groups.currentH4Group);
        }
        groups.currentH4Group = {
          h4Line: line,
          h4Title: extractH4Title(line),
          ols: [],
        };
        groups.currentLevel = 4;
      } else if (line.tag === "ol") {
        result.hasOL = true;

        const characterCount = calculateLineCharacterCount(line);
        const olInfo = {
          line,
          characterCount,
          order: line.order || 0,
        };

        if (groups.currentLevel === 4 && groups.currentH4Group) {
          // 在H4分组中
          groups.currentH4Group.ols.push(olInfo);
        } else if (groups.currentLevel === 3 || !groups.currentH4Group) {
          // 在H3分组中
          if (!groups.currentH3Group) {
            groups.currentH3Group = { ols: [] };
          }
          groups.currentH3Group.ols.push(olInfo);
        }
      } else if (
        (line.tag === "block" || line.tag === "default") &&
        Array.isArray(line.content) &&
        line.content.length > 0
      ) {
        // 🔧 修复：递归处理block和其他容器类型的内容
        // 检查content是否为Line数组（而不是LineTexture数组）
        const firstItem = line.content[0];
        if (firstItem && typeof firstItem === "object" && "tag" in firstItem) {
          // 递归处理Line数组类型的内容
          processLines(line.content as Line[]);
        }
      }
    });
  }

  processLines(lines);

  // 处理最后的分组
  if (groups.currentH3Group && groups.currentH3Group.ols.length > 0) {
    result.h3Groups.push(groups.currentH3Group);
  }
  if (groups.currentH4Group && groups.currentH4Group.ols.length > 0) {
    result.h4Groups.push(groups.currentH4Group);
  }

  // 🔧 修复：重新设置所有分组的OL序号
  result.h3Groups.forEach((group) => {
    group.ols.forEach((ol, index) => {
      ol.order = ol.order || index + 1;
    });
  });

  result.h4Groups.forEach((group) => {
    group.ols.forEach((ol, index) => {
      ol.order = ol.order || index + 1;
    });
  });

  return result;
}

/**
 * 判断单个H3分组的样式
 */
function determineH3GroupStyle(
  group: H3Group,
  groupIndex: number
): GroupStyleResult {
  const olCount = group.ols.length;

  if (olCount >= 4) {
    // 样式1：>=4个序号
    const exceedsLimitOL = group.ols.find((ol) => ol.characterCount > 96);
    if (exceedsLimitOL) {
      return {
        groupType: "h3",
        groupIndex,
        canUseStyle: false,
        styleToApply: 0,
        reason: `H3层级序号${exceedsLimitOL.order}内容${exceedsLimitOL.characterCount}字符，超过96字符限制`,
      };
    }

    return {
      groupType: "h3",
      groupIndex,
      canUseStyle: true,
      styleToApply: 1,
      reason: `H3层级序号${olCount}个，使用样式1`,
      olLines: group.ols.map((ol) => ol.line),
    };
  } else if (olCount === 2 || olCount === 3) {
    // 样式2：2-3个序号
    const exceedsLimitOL = group.ols.find((ol) => ol.characterCount > 75);
    if (exceedsLimitOL) {
      return {
        groupType: "h3",
        groupIndex,
        canUseStyle: false,
        styleToApply: 0,
        reason: `H3层级序号${exceedsLimitOL.order}内容${exceedsLimitOL.characterCount}字符，超过75字符限制`,
      };
    }

    return {
      groupType: "h3",
      groupIndex,
      canUseStyle: true,
      styleToApply: 2,
      reason: `H3层级序号${olCount}个，使用样式2`,
      olLines: group.ols.map((ol) => ol.line),
    };
  }

  return {
    groupType: "h3",
    groupIndex,
    canUseStyle: false,
    styleToApply: 0,
    reason: `H3层级序号${olCount}个，不符合文字排版条件`,
  };
}

/**
 * 判断单个H4分组的样式
 */
function determineH4GroupStyle(
  group: H4Group,
  groupIndex: number
): GroupStyleResult {
  const olCount = group.ols.length;

  if (olCount >= 4) {
    // 样式1：>=4个序号
    const exceedsLimitOL = group.ols.find((ol) => ol.characterCount > 96);
    if (exceedsLimitOL) {
      return {
        groupType: "h4",
        groupIndex,
        canUseStyle: false,
        styleToApply: 0,
        reason: `H4分组${groupIndex + 1}序号${exceedsLimitOL.order}超过96字符限制`,
        h4Title: group.h4Title,
      };
    }

    return {
      groupType: "h4",
      groupIndex,
      canUseStyle: true,
      styleToApply: 1,
      reason: `H4分组${groupIndex + 1}序号${olCount}个，使用样式1`,
      h4Title: group.h4Title,
      olLines: group.ols.map((ol) => ol.line),
    };
  } else if (olCount === 2 || olCount === 3) {
    // 样式3：2-3个序号
    const exceedsLimitOL = group.ols.find((ol) => ol.characterCount > 30);
    if (exceedsLimitOL) {
      return {
        groupType: "h4",
        groupIndex,
        canUseStyle: false,
        styleToApply: 0,
        reason: `H4分组${groupIndex + 1}序号${exceedsLimitOL.order}超过30字符限制`,
        h4Title: group.h4Title,
      };
    }

    return {
      groupType: "h4",
      groupIndex,
      canUseStyle: true,
      styleToApply: 3,
      reason: `H4分组${groupIndex + 1}序号${olCount}个，使用样式3`,
      h4Title: group.h4Title,
      olLines: group.ols.map((ol) => ol.line),
    };
  }

  return {
    groupType: "h4",
    groupIndex,
    canUseStyle: false,
    styleToApply: 0,
    reason:
      olCount > 0
        ? `H4分组${groupIndex + 1}序号${olCount}个，不符合文字排版条件`
        : `H4分组${groupIndex + 1}序号0个，不符合文字排版条件`,
    h4Title: group.h4Title,
  };
}

/**
 * 判断是否可以使用文字排版以及使用哪种样式
 */
export function determineTextLayoutStyle(
  analysis: OLAnalysisResult
): TextLayoutResult {
  if (!analysis.hasOL) {
    return {
      canUseTextLayout: false,
      groupResults: [],
      overallReason: "当前H3不包含有序列表",
      hasAnyApplicableGroup: false,
    };
  }

  const groupResults: GroupStyleResult[] = [];

  // 分析H3分组
  analysis.h3Groups.forEach((group, index) => {
    groupResults.push(determineH3GroupStyle(group, index));
  });

  // 分析H4分组
  analysis.h4Groups.forEach((group, index) => {
    groupResults.push(determineH4GroupStyle(group, index));
  });

  const applicableGroups = groupResults.filter((result) => result.canUseStyle);
  const hasAnyApplicableGroup = applicableGroups.length > 0;

  // 生成整体提示信息
  let overallReason = "";
  if (hasAnyApplicableGroup) {
    const successReasons = applicableGroups.map((result) => result.reason);
    const failureReasons = groupResults
      .filter((result) => !result.canUseStyle)
      .map((result) => result.reason);

    if (failureReasons.length === 0) {
      overallReason = successReasons.join("，");
    } else {
      // 🔧 精简提示信息：当有多个分组时，只显示统计信息
      const h4SuccessCount = applicableGroups.filter(
        (r) => r.groupType === "h4"
      ).length;
      const h4FailureCount = groupResults.filter(
        (r) => r.groupType === "h4" && !r.canUseStyle
      ).length;
      const h3SuccessCount = applicableGroups.filter(
        (r) => r.groupType === "h3"
      ).length;
      const h3FailureCount = groupResults.filter(
        (r) => r.groupType === "h3" && !r.canUseStyle
      ).length;

      const parts = [];
      if (h3SuccessCount > 0) parts.push(`H3层级${h3SuccessCount}个分组可用`);
      if (h4SuccessCount > 0) parts.push(`H4层级${h4SuccessCount}个分组可用`);
      if (h3FailureCount > 0)
        parts.push(`H3层级${h3FailureCount}个分组不符合条件`);
      if (h4FailureCount > 0)
        parts.push(`H4层级${h4FailureCount}个分组不符合条件`);

      overallReason = parts.join("，");
    }
  } else {
    const failureReasons = groupResults.map((result) => result.reason);
    overallReason = failureReasons.join("，");
  }

  return {
    canUseTextLayout: hasAnyApplicableGroup,
    groupResults,
    overallReason,
    hasAnyApplicableGroup,
  };
}

/**
 * 综合分析函数：分析H3部分并返回文字排版建议
 */
export function analyzeTextLayoutForH3Part(lines: Line[]): TextLayoutResult {
  const analysis = analyzeOLStructure(lines);
  return determineTextLayoutStyle(analysis);
}
