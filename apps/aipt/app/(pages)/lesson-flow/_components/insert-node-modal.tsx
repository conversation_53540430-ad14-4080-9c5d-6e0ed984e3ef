import { Button } from "@/app/components/ui/button";
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog";
import { Label } from "@/app/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group";
import emitter from "@/lib/emitter";
import fetcher, { post } from "@/lib/fetcher";
import { useReactFlow } from "@xyflow/react";
import { useSearchParams } from "next/navigation";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import useSWR from "swr";

// 找到当前位置前后的Part组件位置
const findPartBoundaries = (widgets: any[], insertIndex: number) => {
  let prevPartIndex = -1;
  let nextPartIndex = widgets.length;

  // 向前查找最近的Part
  for (let i = insertIndex; i >= 0; i--) {
    if (widgets[i]?.lessonWidgetType === "guide") {
      prevPartIndex = i;
      break;
    }
  }

  // 向后查找最近的Part
  for (let i = insertIndex + 1; i < widgets.length; i++) {
    if (widgets[i]?.lessonWidgetType === "guide") {
      nextPartIndex = i;
      break;
    }
  }

  return { prevPartIndex, nextPartIndex };
};

interface LessonWidget {
  lessonWidgetType: string;
  lessonWidgetName?: string;
  [key: string]: any;
}

interface LessonData {
  widgetList: LessonWidget[];
  [key: string]: any;
}

interface InsertNodeProps {
  onSubmit: () => void;
  setOpen: (open: boolean) => void;
  [key: string]: any;
}

const InsertNode: React.FC<InsertNodeProps> = (props) => {
  const { getNode } = useReactFlow();
  const { onSubmit, setOpen } = props;
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数

  const [fromVal, setFromVal] = useState({
    type: "exercise",
  });

  // 获取当前课程的组件数据，用于检查互动组件数量限制
  const { data: lessonData } = useSWR<any>(
    `/api/v1/lesson_widget?lessonId=${lessonId}`,
    fetcher,
    {
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
    }
  );

  // 使用useMemo优化计算逻辑
  const { interactiveCount, isInteractiveLimitReached, sourceNode } =
    useMemo(() => {
      const widgetList = lessonData?.widgetList ?? [];
      const node = getNode(props.source);
      const currentIndex =
        typeof node?.data?.index === "number" ? node.data.index : 0;

      const { prevPartIndex, nextPartIndex } = findPartBoundaries(
        widgetList,
        currentIndex
      );

      const count = widgetList
        .slice(prevPartIndex + 1, nextPartIndex)
        .filter(
          (widget: any) => widget.lessonWidgetType === "interactive"
        ).length;

      return {
        interactiveCount: count,
        isInteractiveLimitReached: count >= 10,
        sourceNode: node,
      };
    }, [lessonData?.widgetList, props.source, getNode]);

  const createWidget = async () => {
    if (!sourceNode) return;
    return post("/api/v1/lesson_widget/create", {
      arg: {
        lessonId,
        widgetType: fromVal.type,
        previous: sourceNode.data.index,
      },
    });
  };

  const onSubmitHandle = async () => {
    try {
      await createWidget();
      onSubmit();
      setOpen(false);
      if (sourceNode) {
        emitter.emit("openEditModal", {
          lessonId,
          widgetType: fromVal.type,
          previous: sourceNode.data.index,
        });
      }
    } catch (error: any) {
      toast.error(error.message);
    }
  };
  return (
    <div className="insert-node-modal">
      <div className="flex items-center gap-2">
        <div>组件类型：</div>
        <div>
          <RadioGroup
            defaultValue={fromVal.type}
            className="flex"
            onValueChange={(value) => {
              setFromVal({ ...fromVal, type: value });
            }}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="exercise" id="r1" />
              <Label htmlFor="r1">练习组件</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="video" id="r2" />
              <Label htmlFor="r2">视频组件</Label>
            </div>

            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="interactive"
                id="r3"
                disabled={isInteractiveLimitReached}
              />
              <Label
                htmlFor="r3"
                className={
                  isInteractiveLimitReached
                    ? "cursor-not-allowed text-gray-400"
                    : ""
                }
              >
                互动组件 ({interactiveCount}/10)
                {isInteractiveLimitReached && (
                  <span className="text-xs text-red-500"> 已达上限</span>
                )}
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="mt-6 flex justify-end gap-10">
        <DialogClose asChild>
          <Button variant="outline">取消</Button>
        </DialogClose>

        <Button
          onClick={onSubmitHandle}
          disabled={fromVal.type === "interactive" && isInteractiveLimitReached}
        >
          提交
        </Button>
      </div>
    </div>
  );
};

const InsertNodeModal = (props: any) => {
  const [open, setOpen] = useState(false);
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{props.trigger}</DialogTrigger>
      <DialogContent className="bg-white">
        <DialogHeader>
          <DialogTitle>添加组件</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>

        <InsertNode {...props} setOpen={setOpen} />
      </DialogContent>
    </Dialog>
  );
};

export default InsertNodeModal;
