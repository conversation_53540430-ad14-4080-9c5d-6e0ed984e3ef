import { GuideStatus, GuideTaskType } from "./base";
type RawGuide = {
  guideWidgetId: string;
  guideWidgetName: string;
  guideWidgetStatus: GuideStatus;
};

type RawGuideSet = {
  guideWidgetList: RawGuide[];
  guideWidgetSetId: number;
  guideWidgetSetName: string;
  guideWidgetSetStatus: GuideStatus;
  wordNum: number;
  videoLength: number;
};

export interface GenDataFailedTime {
  genFromTranscriptFailedTime: number;
  genVideoJsonFailedTime: number;
  genAudioJsonFailedTime: number;
  genSubtitlesFailedTime: number;
  genBoardscriptFailedTime: number;
  mergeAudioFailedTime: number;
}

export interface SubColumnImage {
  /**
   * 块索引，从1开始
   */
  blockIndex: number;
  /**
   * 图片资源URL
   */
  resourceUrl: string;
  /**
   * 分栏样式，1-5
   */
  styleH3: number;
}

export interface GuideWidget {
  audioJson?: AudioJson | null;
  /**
   * 没有草稿为null
   */
  audioJsonDraft?: AudioJson | null;
  boardscript: string;
  /**
   * 1生成朗读稿和板书和视频
   * 2朗读稿句子生成配音或上传配音
   * 3生成视频数据
   */
  flowRunType: GuideTaskType;
  /**
   * 文稿组件ID
   */
  guideWidgetId: number;
  /**
   * 文稿组件名称
   */
  guideWidgetName: string;
  /**
   * 稿件ID
   */
  guideWidgetSetId: number;
  /**
   * init:待处理 progress:修改中 loading:算法生成中 finish:完成
   */
  guideWidgetStatus: GuideStatus;
  /**
   * 逐字稿
   */
  transcript: string;
  /**
   * 视频更新于（毫秒时间戳）
   */
  videoGenTime: number;
  videoJson: string;
  taskId: string;
  genDataFailedTime: GenDataFailedTime;
  /**
   * 分栏
   */
  subColumn: string;
  /**
   * 分栏图片数据
   */
  subColumnImages?: SubColumnImage[];
  /**
   * 生成图片按钮状态：1可点击，0不可点击
   */
  genImageButtonActive?: number;
  /**
   * 生成互动组件按钮状态：1可点击，0不可点击
   */
  genInteractiveButtonActive?: number;
  /**
   * 互动组件列表
   */
  baseWidgetList?: BaseWidget[];
}

/**
 * 互动组件数据结构
 */
export interface BaseWidget {
  baseWidgetId: string;
  widgetOri: "ai" | "local";
  widgetName?: string;
  data: {
    url: string;
    typeName: string;
  };
}

/**
 * 朗读稿
 */
export interface AudioJson {
  audioUrl: string;
  sentenceList: AudioSentence[];
}

/**
 * 没有草稿为null
 */
export interface AudioJsonDraft {
  /**
   * 整段草稿
   */
  audioUrl: string;
  sentenceList: AudioSentence[];
}

export interface AudioSentence {
  audioSentenceText: string;
  audioSentenceUrl: string;
  audioWordsUrl: string;
  index?: number;
  isCompleted?: boolean;
  uploadMark?: boolean;
  changeType?: number;
  genStatus?: number;
}

export type { RawGuide, RawGuideSet };
