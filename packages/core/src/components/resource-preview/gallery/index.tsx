"use client";
import { cn } from "@repo/ui/lib/utils";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { IPhotoLoadedParams } from "./Photo";
import PhotoSlider from "./PhotoSlider";
import { OverlayRenderProps } from "./types";

interface GalleryProps {
  imgInfos: {
    url: string;
    name: string;
  }[];
  onClose?: () => void;
  onIndexChange?: (index: number) => void;
  onPhotoLoad?: (index: number, params: IPhotoLoadedParams) => void;
}

const Overlay = memo(
  function Overlay({
    images,
    onIndexChange,
    index,
    overlayVisible,
  }: OverlayRenderProps) {
    return (
      <div
        className={cn(
          "fixed bottom-0 left-0 z-10 flex h-20 w-full items-center justify-center gap-3 bg-[#F7F6F5] backdrop-blur-[6px]",
          !overlayVisible && "hidden"
        )}
        style={{
          background: `linear-gradient(81.96deg, rgba(245, 250, 255, 0.1105) 29.87%, rgba(250, 252, 255, 0.65) 48.03%, rgba(255, 255, 255, 0.1105) 66.18%)`,
        }}
      >
        {images.map((e, i) => (
          <div
            className={cn(
              `bg-gray flex h-12 w-12 items-center justify-center rounded-md border-[1px]`,
              index === i ? "border-[#6574FC]" : "border-[#E9ECF5]"
            )}
            key={e.key}
            onClick={() => onIndexChange(i)}
          >
            <img src={e.src} className="max-h-full max-w-full object-cover" />
          </div>
        ))}
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.overlayVisible === nextProps.overlayVisible &&
      prevProps.index === nextProps.index
    );
  }
);

export default memo(function Gallery({
  imgInfos,
  onClose,
  onIndexChange,
  onPhotoLoad,
}: GalleryProps) {
  const [visible, setVisible] = useState(true);

  const handleGalleryClose = useCallback(() => {
    setVisible(false);
    onClose?.();
  }, [onClose]);

  const images = useMemo(() => {
    return imgInfos.map((info) => ({
      key: info.url,
      src: info.url,
      name: info.name,
    }));
  }, [imgInfos]);

  const handleIndexChange = useCallback(
    (index: number) => {
      onIndexChange?.(index);
    },
    [onIndexChange]
  );

  const handlePhotoLoad = useCallback(
    (index: number, params: IPhotoLoadedParams) => {
      onPhotoLoad?.(index, params);
    },
    [onPhotoLoad]
  );

  useEffect(() => {
    onIndexChange?.(0);
  }, []);

  return (
    <PhotoSlider
      className="fixed left-0 top-0 z-0 h-full w-full bg-[#F7F6F5]"
      maskOpacity={1}
      visible={visible}
      images={images}
      onClose={handleGalleryClose}
      maskClosable={false}
      bannerVisible={false}
      pullClosable={false}
      photoClosable={false}
      Overlay={Overlay}
      portalContainer={document.body}
      onIndexChange={handleIndexChange}
      onPhotoLoad={handlePhotoLoad}
    />
  );
});
