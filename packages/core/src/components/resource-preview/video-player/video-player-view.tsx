import { cn } from "@repo/ui/lib/utils";
import dynamic from "next/dynamic";
import { FC, useCallback } from "react";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";
import { useVideoViewContext } from "./video-view-context";

const VolcengineVideo = dynamic(
  () => import("@repo/core/components/volcengine-video/volcengine-video"),
  {
    ssr: false,
  }
);
export const VideoPlayerView: FC<{ className?: string }> = ({ className }) => {
  const {
    refVolcenginePlayer,
    playRate,
    set3XPlayRate,
    resetPlayRate,
    togglePlayerControls,
    togglePlay,
    userId,
    url,
  } = useVideoViewContext();

  const longPressHandlers = useLongPress(set3XPlayRate, {
    onFinish: () => {
      resetPlayRate();
      // trackEventWithLessonId("doc_fast_forward_longpress");
    },
    // onCancel: () => {
    //   console.log("onCancel");
    //   resetPlayRate();
    // },
    detect: LongPressEventType.Touch,
  });
  const handleClick = useCallback(() => {
    togglePlayerControls();
  }, [togglePlayerControls]);

  const handleDoubleClick = useCallback(() => {
    togglePlay();
    // trackEventWithLessonId("doc_play_pause_doubleclick");
  }, [togglePlay]);

  const doubleTapHandlers = useDoubleTap(handleDoubleClick, 300, {
    onSingleTap: handleClick,
  });

  // if (!active) {
  //   return null;
  // }

  return (
    <div
      data-name="video-player"
      className={cn("h-full w-full", className)}
      {...doubleTapHandlers}
      {...longPressHandlers()}
    >
      <VolcengineVideo
        className="flex h-full w-full items-center justify-center"
        ref={refVolcenginePlayer}
        src={url}
        playRate={playRate.value}
        userId={userId}
        tag="视频组件"
        playerConfig={{
          fluid: false,
        }}
      />
    </div>
  );
};
