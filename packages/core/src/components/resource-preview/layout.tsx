"use client";

import dynamic from "next/dynamic";
import { memo, useCallback, useMemo } from "react";
import IconClose from "./assets/ic_close.svg";

import { ALI_IMM_SDK_URL } from "@repo/core/components/doc-preview/const";
import useScriptLoaded from "@repo/core/components/doc-preview/hooks/useScriptLoaded";
import type { WebOfficePreviewProps } from "@repo/core/components/doc-preview/index";
import VolcengineLicense from "@repo/core/components/volcengine-video/volcengine-license";
import { cn } from "@repo/ui/lib/utils";
import Script from "next/script";
import { WebTokenInfo } from "../doc-preview/type";
import { Skeleton } from "./_components/Skeleton";

const VideoPlayer = dynamic(
  () => import("@repo/core/components/resource-preview/video-player"),
  {
    ssr: false,
  }
);

const AudioPlayer = dynamic(
  () => import("@repo/core/components/resource-preview/audio-player"),
  {
    ssr: false,
  }
);

const WebOfficePreview = dynamic(
  () => import("@repo/core/components/doc-preview/index"),
  {
    ssr: false,
  }
);

const Gallery = dynamic(
  () => import("@repo/core/components/resource-preview/gallery"),
  {
    ssr: false,
  }
);

export type ResourceData = {
  originFileName: string;
  ossUrl: string;
  vodUrl?: string;
  webOffice?: WebTokenInfo;
};

export type ResourcePreviewerLayoutProps = {
  resourcesData?: ResourceData[];
  isFetchingResourcesData?: boolean;
  visible: boolean;
  onClose?: () => void;
  showClose?: boolean;
  className?: string;
  style?: React.CSSProperties;

  closeButtonClassName?: string;
  closeButtonStyle?: React.CSSProperties;

  getResourceDocumentRefreshToken: WebOfficePreviewProps["refreshTokenRequest"];

  onResourceReady?: (resourcesData: ResourceData, index: number) => void;
  onPreview?: (resourcesData: ResourceData, index: number) => void;
};

const mappingFileType = (filename: string) => {
  const ext = filename.split(".").pop();
  if (ext === "doc" || ext === "docx") return "document";
  if (ext === "xls" || ext === "xlsx") return "document";
  if (ext === "ppt" || ext === "pptx") return "document";
  if (ext === "pdf") return "document";
  if (ext === "jpg" || ext === "jpeg" || ext === "png" || ext === "gif")
    return "image";
  if (ext === "mp4" || ext === "avi") return "video";
  if (ext === "mp3") return "audio";
  return "unknown";
};

const CloseButton = memo(function CloseButton({
  onClose,
  className,
  style,
}: {
  onClose?: () => void;
  className?: string;
  style?: React.CSSProperties;
}) {
  return (
    <button
      className={cn(
        "z-3000 h-9.5 w-9.5 absolute right-7 top-2 inline-flex cursor-pointer items-center justify-center whitespace-nowrap rounded-md border border-slate-200 bg-white p-0 text-sm font-medium text-slate-600 transition-colors hover:bg-slate-50 focus-visible:outline-none active:bg-slate-200",
        className
      )}
      style={style}
      onClick={onClose}
    >
      <IconClose />
    </button>
  );
});

export default memo(function ResourcePreviewerLayout(
  props: ResourcePreviewerLayoutProps
) {
  const {
    resourcesData,
    isFetchingResourcesData,
    visible,
    onClose,
    className,
    style,
    getResourceDocumentRefreshToken,

    closeButtonClassName,
    closeButtonStyle,
    showClose = true,
    onPreview,
    onResourceReady,
  } = props;
  const { status, handleScriptLoad, handleScriptLoadError } = useScriptLoaded();

  const resourceType = useMemo(() => {
    if (resourcesData?.[0]?.originFileName === undefined) return "unknown";

    return mappingFileType(resourcesData?.[0]?.originFileName);
  }, [resourcesData]);

  const handleClose = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const handleVideoError = useCallback(() => {
    handleClose();
  }, [handleClose]);

  const handlePreview = useCallback(
    (data: ResourceData, index: number) => {
      onPreview?.(data, index);
    },
    [onPreview]
  );

  const handleResourceReady = useCallback(
    (data: ResourceData, index: number) => {
      onResourceReady?.(data, index);
    },
    [onResourceReady]
  );

  if (!visible) return null;

  let content: React.ReactNode = null;

  if (
    isFetchingResourcesData === true ||
    resourcesData === undefined ||
    resourcesData?.length === 0
  ) {
    content = <Skeleton className="h-full w-full" />;
  } else if (resourceType === "image") {
    content = (
      <Gallery
        imgInfos={resourcesData.map((e) => ({
          url: e.ossUrl,
          name: e.originFileName,
        }))}
        onClose={handleClose}
        onIndexChange={(index) => handlePreview(resourcesData[index]!, index)}
        onPhotoLoad={(index) => {
          handleResourceReady(resourcesData[index]!, index);
        }}
      />
    );
  } else if (resourceType === "document") {
    content = (
      <WebOfficePreview
        onPreview={() => {
          handlePreview(resourcesData[0]!, 0);
          handleResourceReady(resourcesData[0]!, 0);
        }}
        key={resourcesData?.[0]?.originFileName}
        tokenInfo={resourcesData?.[0]?.webOffice}
        scriptLoaded={status.value === "loaded"}
        enableJumpToPPTFirstSlide
        enableMobileAdapt
        refreshTokenRequest={getResourceDocumentRefreshToken}
      />
    );
  } else if (
    resourceType === "video" &&
    resourcesData?.[0]?.vodUrl !== undefined
  ) {
    content = (
      <VideoPlayer
        key={resourcesData[0].originFileName}
        className="h-full w-full"
        onError={handleVideoError}
        src={resourcesData[0].vodUrl}
        onCanPlay={() => {
          handleResourceReady(resourcesData[0]!, 0);
        }}
        onPlay={() => {
          handlePreview?.(resourcesData[0]!, 0);
        }}
      />
    );
  } else if (
    resourceType === "audio" &&
    resourcesData?.[0]?.ossUrl !== undefined
  ) {
    content = (
      <AudioPlayer
        key={resourcesData?.[0]?.originFileName}
        src={resourcesData[0].ossUrl}
        onCanPlay={() => handleResourceReady(resourcesData[0]!, 0)}
        onPlay={() => handlePreview(resourcesData[0]!, 0)}
      />
    );
  }

  if (content)
    return (
      <div
        className={cn("relative h-full w-full bg-[#F7F6F5]", className)}
        style={style}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        {showClose === true ? (
          <CloseButton
            onClose={handleClose}
            className={closeButtonClassName}
            style={closeButtonStyle}
          />
        ) : null}
        <VolcengineLicense />
        <Script
          src={ALI_IMM_SDK_URL}
          onLoad={handleScriptLoad}
          onReady={handleScriptLoad}
          onError={handleScriptLoadError}
        />
        {content}
      </div>
    );
});
