import { useSignal } from "@preact-signals/safe-react";
import lottie3dot from "@repo/core/assets/guide-theme/3dot.json";
import ArrowUp from "@repo/core/assets/guide-theme/arrow-up.svg";
import { cn } from "@repo/ui/lib/utils";
import Lottie from "lottie-react";
import { FC, RefObject, useEffect, useMemo, useRef } from "react";
import { createPortal } from "react-dom";
import { match } from "ts-pattern";
import { useThrottledCallback } from "use-debounce";

enum Direction {
  Prev = -1,
  Next = 1,
}

enum FlipStatus {
  Idle = "idle",
  Scrolling = "scrolling",
  Ready = "ready",
  Flipping = "flipping",
}

export const ScrollFlip: FC<{
  refContainer: RefObject<HTMLDivElement | null>;
  refBuddy: RefObject<HTMLDivElement | null>;
  onFlipped?: () => void;
  direction: Direction;
}> = ({ refContainer, refBuddy, onFlipped, direction }) => {
  const ref = useRef(null);
  const status = useSignal(FlipStatus.Idle);

  const scrollingTip = useMemo(() => {
    return direction === Direction.Prev ? "滑动返回上一节" : "滑动进入下一节";
  }, [direction]);

  const readyTip = useMemo(() => {
    return direction === Direction.Prev ? "松手返回上一节" : "松手进入下一节";
  }, [direction]);

  const tip = useMemo(() => {
    return match(status.value)
      .with(FlipStatus.Scrolling, () => (
        <>
          <ArrowUp
            className={cn(
              "size-10",
              direction === Direction.Prev && "rotate-180"
            )}
          />
          {scrollingTip}
        </>
      ))
      .with(FlipStatus.Ready, () => (
        <>
          <Lottie
            className="m-2 size-6"
            animationData={lottie3dot}
            autoPlay={true}
            loop={true}
          />
          {readyTip}
        </>
      ))
      .with(FlipStatus.Flipping, () => (
        <>
          <Lottie
            className="m-2 size-6"
            animationData={lottie3dot}
            autoPlay={true}
            loop={true}
          />
          <div>&nbsp;</div>
        </>
      ))
      .otherwise(() => null);
  }, [status.value, scrollingTip, readyTip]);

  const actionFinish = useThrottledCallback(() => {
    if (status.value === FlipStatus.Ready) {
      status.value = FlipStatus.Flipping;
      onFlipped?.();
      requestIdleCallback(
        () => {
          status.value = FlipStatus.Idle;
        },
        { timeout: 1000 }
      );
    }
  }, 200);

  useEffect(() => {
    if (!refContainer.current) return;
    if (!ref.current) return;

    const container = refContainer.current;
    const scrollingOption = {
      root: container,
      rootMargin: "0px",
      threshold: 0.01,
    };
    const readyOption = {
      root: container,
      rootMargin: "0px",
      threshold: 0.98,
      delay: 300, // 延迟200ms后触发ready状态
    };

    const readyObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          status.value = FlipStatus.Ready;
        }
      });
    }, readyOption);

    const scrollingObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        // 完成翻页后再回复idle状态
        if (status.value === FlipStatus.Flipping) return;
        if (entry.isIntersecting) {
          status.value = FlipStatus.Scrolling;
        } else {
          status.value = FlipStatus.Idle;
        }
      });
    }, scrollingOption);
    readyObserver.observe(ref.current);
    scrollingObserver.observe(ref.current);

    return () => {
      scrollingObserver.disconnect();
      readyObserver.disconnect();
    };
  }, [refContainer, ref, status]);

  useEffect(() => {
    if (!refContainer.current) return;
    if (!ref.current) return;
    const container = refContainer.current;
    container.addEventListener("wheel", actionFinish);
    container.addEventListener("touchend", actionFinish);
    return () => {
      container.removeEventListener("wheel", actionFinish);
      container.removeEventListener("touchend", actionFinish);
    };
  }, [refContainer, ref, actionFinish]);

  return match(direction)
    .with(Direction.Prev, () => {
      return (
        <>
          {/* 回弹区域 */}
          <div ref={ref} className={cn("flex h-20 w-full")} />
          {
            /* 提示 */
            createPortal(
              <div className="text-text-4 pointer-events-none fixed left-0 top-16 flex h-auto w-full flex-col items-center justify-center text-xs font-normal">
                {tip}
              </div>,
              refBuddy.current ?? document.body
            )
          }
          {/* 回弹位置 */}
          <div className="h-0 snap-start snap-always" />
        </>
      );
    })
    .with(Direction.Next, () => {
      return (
        <>
          {/* 回弹位置 */}
          <div className="h-0 snap-end snap-always" />
          {
            /* 提示 */
            createPortal(
              <div className="text-text-4 pointer-events-none fixed bottom-20 left-0 flex h-auto w-full flex-col items-center justify-center text-xs font-normal">
                {tip}
              </div>,
              refBuddy.current ?? document.body
            )
          }
          {/* 回弹区域 */}
          <div ref={ref} className={cn("flex h-20 w-full")} />
        </>
      );
    })
    .otherwise(() => null);
};

export { Direction };
