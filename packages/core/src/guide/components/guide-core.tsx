import { Layer, LayerItem } from "@repo/core/components/layer";
import { Subtitle } from "@repo/core/guide/components/subtitle";
import { ComponentProps, FC, useRef } from "react";
import { Video } from "remotion";
import { useGuideContext } from "../context/guide-context";

import ImageWatermark from "@repo/core/assets/images/watermark.png";
import { cn } from "@repo/ui/lib/utils";
import Image from "next/image";
import { BtnJumpLine } from "./guide-line";
import { GuideSectionH2 } from "./guide-section-h2";
import { Direction, ScrollFlip } from "./scroll-flip";

export const GuideCore: FC<ComponentProps<"div">> = () => {
  const {
    index,
    totalGuideCount,
    data,
    showSubtitle,
    refContainer,
    selectedLine,
    needScrollFlip,
    onScrollFlip,
    client,
  } = useGuideContext();

  const refFlipContainer = useRef<HTMLDivElement>(null);
  const { avatar, subtitles } = data;
  return (
    <div className="guide-view relative flex h-full w-full bg-[#FAF8F6]">
      <Layer className="font-resource-han-rounded">
        <LayerItem index={2} className="h-full w-full">
          <main
            ref={refFlipContainer}
            className={cn(
              "w-full overflow-y-auto overscroll-contain scroll-smooth",
              needScrollFlip && "snap-y snap-mandatory",
              client === "stu" ? "h-screen" : "h-full"
            )}
          >
            {needScrollFlip &&
              index > 0 && ( // 用于触发滚动到顶部
                <ScrollFlip
                  refContainer={refFlipContainer}
                  refBuddy={refContainer}
                  direction={Direction.Prev}
                  onFlipped={() => {
                    onScrollFlip?.(index - 1);
                  }}
                />
              )}
            <div
              ref={refContainer}
              onClick={() => {
                selectedLine.value = null;
              }}
              data-name="guide-container"
              className={cn(
                // "pointer-events-auto", // 这个地方是个Magic代码，可以避免某些情况出现滚动条
                "relative h-full w-full overflow-y-auto scroll-smooth"
              )}
            >
              <GuideSectionH2 />
              <BtnJumpLine />
            </div>

            {needScrollFlip &&
              index < totalGuideCount && ( // 用于触发滚动到底部
                <ScrollFlip
                  refContainer={refFlipContainer}
                  refBuddy={refContainer}
                  direction={Direction.Next}
                  onFlipped={() => {
                    onScrollFlip?.(index + 1);
                  }}
                />
              )}
          </main>
          {showSubtitle && (
            <div className="right-30 fixed bottom-8 left-1/2 z-20 -translate-x-[50%]">
              <Subtitle subtitles={subtitles} />
            </div>
          )}
        </LayerItem>
        <LayerItem
          index={1}
          className="max-w-1/5 right-0 w-[calc(100%-var(--width-guide))]"
        >
          <div className="relative flex h-full w-full flex-col items-center justify-end">
            {client !== "stu" && avatar.url && (
              <>
                <Image
                  src={ImageWatermark}
                  alt="watermark"
                  className="right-4.5 absolute bottom-3 w-[56px] bg-transparent"
                  unoptimized
                />
                <Video
                  src={avatar.url}
                  pauseWhenBuffering
                  crossOrigin="anonymous"
                  onError={(e) => {
                    console.log(e);
                  }}
                />
              </>
            )}
          </div>
        </LayerItem>
      </Layer>
    </div>
  );
};
